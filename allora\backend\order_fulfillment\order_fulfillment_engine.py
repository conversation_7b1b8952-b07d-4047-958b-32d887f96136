"""
Order Fulfillment Engine
========================

Comprehensive order fulfillment engine that automates order processing from placement
to shipment creation. Integrates with the carrier integration system and existing
order management to provide complete fulfillment automation.

Key Features:
1. Automated Order Processing
2. Inventory Allocation and Validation
3. Intelligent Carrier Selection
4. Business Rules Engine
5. Fulfillment Workflow Management
6. Real-time Status Updates
7. Exception Handling and Recovery
8. Performance Optimization

Integration Points:
- Existing Order/OrderItem models
- Carrier Integration System
- Inventory Management
- Notification System
"""

import logging
import json
import uuid
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor
import threading
from queue import Queue, PriorityQueue

# Import Flask and database
from flask import current_app
from sqlalchemy import and_, or_
from sqlalchemy.orm import joinedload

# Import our systems
from .carrier_integration import (
    CarrierFactory, RateCalculationEngine, ShipmentManager,
    CarrierAPIError, ShippingCarrier
)
from .order_fulfillment_architecture import (
    Address, Package, FulfillmentRequest, FulfillmentPriority,
    FulfillmentConfig, ShipmentStatus, TrackingEventType
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ============================================================================
# ENUMS AND DATA STRUCTURES
# ============================================================================

class FulfillmentStatus(Enum):
    """Order fulfillment status"""
    PENDING = "pending"
    PROCESSING = "processing"
    INVENTORY_ALLOCATED = "inventory_allocated"
    CARRIER_SELECTED = "carrier_selected"
    LABEL_CREATED = "label_created"
    PICKUP_SCHEDULED = "pickup_scheduled"
    SHIPPED = "shipped"
    DELIVERED = "delivered"
    EXCEPTION = "exception"
    CANCELLED = "cancelled"

class BusinessRuleType(Enum):
    """Types of business rules"""
    AUTO_FULFILL = "auto_fulfill"
    REQUIRE_APPROVAL = "require_approval"
    CARRIER_SELECTION = "carrier_selection"
    SHIPPING_METHOD = "shipping_method"
    INSURANCE = "insurance"
    SIGNATURE = "signature"
    PRIORITY = "priority"

@dataclass
class FulfillmentRule:
    """Business rule for fulfillment decisions"""
    rule_type: BusinessRuleType
    condition: Dict[str, Any]  # Condition to evaluate
    action: Dict[str, Any]     # Action to take if condition is met
    priority: int = 1          # Rule priority (lower = higher priority)
    is_active: bool = True
    
    def evaluate(self, context: Dict[str, Any]) -> bool:
        """Evaluate if this rule applies to the given context"""
        try:
            # Simple condition evaluation (can be enhanced with complex logic)
            for key, expected_value in self.condition.items():
                if key not in context:
                    return False
                
                actual_value = context[key]
                
                # Handle different comparison types
                if isinstance(expected_value, dict):
                    if 'min' in expected_value and actual_value < expected_value['min']:
                        return False
                    if 'max' in expected_value and actual_value > expected_value['max']:
                        return False
                    if 'equals' in expected_value and actual_value != expected_value['equals']:
                        return False
                    if 'in' in expected_value and actual_value not in expected_value['in']:
                        return False
                else:
                    if actual_value != expected_value:
                        return False
            
            return True
        except Exception as e:
            logger.error(f"Error evaluating rule: {e}")
            return False

@dataclass
class FulfillmentContext:
    """Context information for fulfillment processing"""
    order_id: int
    order_total: float
    order_weight: float
    order_items: List[Dict[str, Any]]
    shipping_address: Address
    billing_address: Optional[Address]
    customer_type: str  # 'guest', 'registered', 'premium'
    shipping_method: str
    special_instructions: Optional[str]
    is_international: bool
    requires_signature: bool
    requires_insurance: bool
    priority: FulfillmentPriority
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for rule evaluation"""
        return {
            'order_id': self.order_id,
            'order_total': self.order_total,
            'order_weight': self.order_weight,
            'item_count': len(self.order_items),
            'customer_type': self.customer_type,
            'shipping_method': self.shipping_method,
            'is_international': self.is_international,
            'requires_signature': self.requires_signature,
            'requires_insurance': self.requires_insurance,
            'priority': self.priority.value,
            'destination_country': self.shipping_address.country,
            'destination_state': self.shipping_address.state
        }

@dataclass
class FulfillmentResult:
    """Result of fulfillment processing"""
    success: bool
    order_id: int
    fulfillment_id: Optional[str]
    status: FulfillmentStatus
    carrier: Optional[ShippingCarrier]
    tracking_number: Optional[str]
    shipping_cost: Optional[float]
    estimated_delivery: Optional[datetime]
    label_url: Optional[str]
    error_message: Optional[str]
    processing_time: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'success': self.success,
            'order_id': self.order_id,
            'fulfillment_id': self.fulfillment_id,
            'status': self.status.value,
            'carrier': self.carrier.value if self.carrier else None,
            'tracking_number': self.tracking_number,
            'shipping_cost': self.shipping_cost,
            'estimated_delivery': self.estimated_delivery.isoformat() if self.estimated_delivery else None,
            'label_url': self.label_url,
            'error_message': self.error_message,
            'processing_time': self.processing_time
        }

# ============================================================================
# BUSINESS RULES ENGINE
# ============================================================================

class BusinessRulesEngine:
    """Engine for evaluating and applying business rules"""
    
    def __init__(self):
        self.rules: List[FulfillmentRule] = []
        self._load_default_rules()
    
    def _load_default_rules(self):
        """Load default fulfillment rules"""
        config = FulfillmentConfig()
        
        # Auto-fulfill rule for small orders
        self.rules.append(FulfillmentRule(
            rule_type=BusinessRuleType.AUTO_FULFILL,
            condition={'order_total': {'max': config.DEFAULT_FULFILLMENT_RULES['auto_fulfill_threshold']}},
            action={'auto_fulfill': True},
            priority=1
        ))
        
        # Require approval for large orders
        self.rules.append(FulfillmentRule(
            rule_type=BusinessRuleType.REQUIRE_APPROVAL,
            condition={'order_total': {'min': config.DEFAULT_FULFILLMENT_RULES['require_approval_threshold']}},
            action={'require_approval': True},
            priority=1
        ))
        
        # Default carrier selection
        self.rules.append(FulfillmentRule(
            rule_type=BusinessRuleType.CARRIER_SELECTION,
            condition={'destination_country': 'India'},
            action={'preferred_carrier': config.DEFAULT_FULFILLMENT_RULES['default_carrier'].value},
            priority=2
        ))
        
        # Insurance rule
        self.rules.append(FulfillmentRule(
            rule_type=BusinessRuleType.INSURANCE,
            condition={'order_total': {'min': config.DEFAULT_FULFILLMENT_RULES['insurance_threshold']}},
            action={'require_insurance': True},
            priority=1
        ))
        
        # Signature rule
        self.rules.append(FulfillmentRule(
            rule_type=BusinessRuleType.SIGNATURE,
            condition={'order_total': {'min': config.DEFAULT_FULFILLMENT_RULES['signature_threshold']}},
            action={'require_signature': True},
            priority=1
        ))
    
    def add_rule(self, rule: FulfillmentRule):
        """Add a new business rule"""
        self.rules.append(rule)
        # Sort by priority
        self.rules.sort(key=lambda r: r.priority)
    
    def evaluate_rules(self, context: FulfillmentContext) -> Dict[str, Any]:
        """Evaluate all applicable rules for the given context"""
        decisions = {}
        context_dict = context.to_dict()
        
        for rule in self.rules:
            if not rule.is_active:
                continue
                
            if rule.evaluate(context_dict):
                # Apply rule action
                decisions.update(rule.action)
                logger.info(f"Applied rule {rule.rule_type.value} for order {context.order_id}")
        
        return decisions

# ============================================================================
# INVENTORY ALLOCATION SYSTEM
# ============================================================================

class InventoryAllocator:
    """Handles inventory allocation and validation for orders"""

    def __init__(self, db_session):
        self.db = db_session

    def allocate_inventory(self, order_items: List[Dict[str, Any]]) -> Tuple[bool, List[Dict[str, Any]]]:
        """
        Allocate inventory for order items
        Returns (success, allocation_details)
        """
        allocation_details = []

        try:
            # Import here to avoid circular imports
            from app import Product

            for item in order_items:
                product_id = item['product_id']
                quantity_needed = item['quantity']

                # Get product with lock to prevent race conditions
                product = self.db.query(Product).filter(
                    Product.id == product_id
                ).with_for_update().first()

                if not product:
                    allocation_details.append({
                        'product_id': product_id,
                        'status': 'error',
                        'message': 'Product not found',
                        'allocated_quantity': 0
                    })
                    continue

                # Check availability
                if product.stock_quantity < quantity_needed:
                    allocation_details.append({
                        'product_id': product_id,
                        'status': 'insufficient_stock',
                        'message': f'Only {product.stock_quantity} available, need {quantity_needed}',
                        'allocated_quantity': 0,
                        'available_quantity': product.stock_quantity
                    })
                    continue

                # Allocate inventory (reduce stock)
                product.stock_quantity -= quantity_needed

                allocation_details.append({
                    'product_id': product_id,
                    'status': 'allocated',
                    'message': 'Inventory allocated successfully',
                    'allocated_quantity': quantity_needed,
                    'remaining_quantity': product.stock_quantity
                })

                logger.info(f"Allocated {quantity_needed} units of product {product_id}")

            # Check if all allocations were successful
            success = all(detail['status'] == 'allocated' for detail in allocation_details)

            if not success:
                # Rollback allocations if any failed
                self.db.rollback()
                logger.warning("Inventory allocation failed, rolling back")

            return success, allocation_details

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error allocating inventory: {e}")
            return False, [{'status': 'error', 'message': str(e)}]

    def release_inventory(self, order_items: List[Dict[str, Any]]) -> bool:
        """Release allocated inventory back to stock"""
        try:
            from app import Product

            for item in order_items:
                product_id = item['product_id']
                quantity_to_release = item['quantity']

                product = self.db.query(Product).filter(
                    Product.id == product_id
                ).with_for_update().first()

                if product:
                    product.stock_quantity += quantity_to_release
                    logger.info(f"Released {quantity_to_release} units of product {product_id}")

            return True

        except Exception as e:
            logger.error(f"Error releasing inventory: {e}")
            return False

# ============================================================================
# FULFILLMENT WORKFLOW MANAGER
# ============================================================================

class FulfillmentWorkflowManager:
    """Manages the fulfillment workflow state machine"""

    # Define valid state transitions
    VALID_TRANSITIONS = {
        FulfillmentStatus.PENDING: [FulfillmentStatus.PROCESSING, FulfillmentStatus.CANCELLED],
        FulfillmentStatus.PROCESSING: [FulfillmentStatus.INVENTORY_ALLOCATED, FulfillmentStatus.EXCEPTION],
        FulfillmentStatus.INVENTORY_ALLOCATED: [FulfillmentStatus.CARRIER_SELECTED, FulfillmentStatus.EXCEPTION],
        FulfillmentStatus.CARRIER_SELECTED: [FulfillmentStatus.LABEL_CREATED, FulfillmentStatus.EXCEPTION],
        FulfillmentStatus.LABEL_CREATED: [FulfillmentStatus.PICKUP_SCHEDULED, FulfillmentStatus.SHIPPED],
        FulfillmentStatus.PICKUP_SCHEDULED: [FulfillmentStatus.SHIPPED, FulfillmentStatus.EXCEPTION],
        FulfillmentStatus.SHIPPED: [FulfillmentStatus.DELIVERED],
        FulfillmentStatus.EXCEPTION: [FulfillmentStatus.PROCESSING, FulfillmentStatus.CANCELLED],
        FulfillmentStatus.DELIVERED: [],  # Terminal state
        FulfillmentStatus.CANCELLED: []   # Terminal state
    }

    def __init__(self, db_session):
        self.db = db_session

    def can_transition(self, current_status: FulfillmentStatus, new_status: FulfillmentStatus) -> bool:
        """Check if transition from current to new status is valid"""
        return new_status in self.VALID_TRANSITIONS.get(current_status, [])

    def update_fulfillment_status(self, order_id: int, new_status: FulfillmentStatus,
                                 details: Optional[Dict[str, Any]] = None) -> bool:
        """Update fulfillment status with validation"""
        try:
            # Import here to avoid circular imports
            from app import Order

            order = self.db.query(Order).filter(Order.id == order_id).first()
            if not order:
                logger.error(f"Order {order_id} not found")
                return False

            # Get current fulfillment status (stored in order status for now)
            current_status = FulfillmentStatus(order.status) if order.status else FulfillmentStatus.PENDING

            # Validate transition
            if not self.can_transition(current_status, new_status):
                logger.error(f"Invalid transition from {current_status.value} to {new_status.value} for order {order_id}")
                return False

            # Update status
            order.status = new_status.value

            # Update additional fields based on status
            if new_status == FulfillmentStatus.SHIPPED:
                # Update order status to shipped
                order.status = 'shipped'
            elif new_status == FulfillmentStatus.DELIVERED:
                order.status = 'delivered'
            elif new_status == FulfillmentStatus.CANCELLED:
                order.status = 'cancelled'

            # Log status change
            logger.info(f"Updated order {order_id} status from {current_status.value} to {new_status.value}")

            # Trigger notifications (integrate with existing notification system)
            self._trigger_status_notification(order_id, new_status, details)

            return True

        except Exception as e:
            logger.error(f"Error updating fulfillment status: {e}")
            return False

    def _trigger_status_notification(self, order_id: int, status: FulfillmentStatus,
                                   details: Optional[Dict[str, Any]] = None):
        """Trigger status update notifications"""
        try:
            # FIXED: Import Flask-SocketIO notification system instead of deprecated websocket_manager
            from flask_socketio_manager import send_order_status_update

            # Get user ID from order
            from app import Order
            order = self.db.query(Order).filter(Order.id == order_id).first()

            if order and order.user_id:
                # Trigger WebSocket notification using Flask-SocketIO
                send_order_status_update(str(order.user_id), order_id, status.value)

        except Exception as e:
            logger.error(f"Error triggering status notification: {e}")

# ============================================================================
# CORE ORDER FULFILLMENT ENGINE
# ============================================================================

class OrderFulfillmentEngine:
    """
    Core order fulfillment engine that orchestrates the entire fulfillment process
    """

    def __init__(self, db_session):
        self.db = db_session
        self.rules_engine = BusinessRulesEngine()
        self.inventory_allocator = InventoryAllocator(db_session)
        self.workflow_manager = FulfillmentWorkflowManager(db_session)
        self.carrier_factory = CarrierFactory()
        self.rate_engine = RateCalculationEngine()
        self.shipment_manager = ShipmentManager()

        # Processing queue for batch operations
        self.processing_queue = PriorityQueue()
        self.executor = ThreadPoolExecutor(max_workers=4)

        logger.info("Order Fulfillment Engine initialized")

    def process_order(self, order_id: int, priority: FulfillmentPriority = FulfillmentPriority.NORMAL) -> FulfillmentResult:
        """
        Process a single order through the complete fulfillment workflow
        """
        start_time = datetime.now()
        fulfillment_id = f"FUL-{uuid.uuid4().hex[:8].upper()}"

        try:
            logger.info(f"Starting fulfillment process for order {order_id}")

            # Step 1: Load and validate order
            context = self._create_fulfillment_context(order_id, priority)
            if not context:
                return FulfillmentResult(
                    success=False,
                    order_id=order_id,
                    fulfillment_id=fulfillment_id,
                    status=FulfillmentStatus.EXCEPTION,
                    carrier=None,
                    tracking_number=None,
                    shipping_cost=None,
                    estimated_delivery=None,
                    label_url=None,
                    error_message="Failed to create fulfillment context",
                    processing_time=(datetime.now() - start_time).total_seconds()
                )

            # Step 2: Apply business rules
            self.workflow_manager.update_fulfillment_status(order_id, FulfillmentStatus.PROCESSING)
            decisions = self.rules_engine.evaluate_rules(context)

            # Check if order requires approval
            if decisions.get('require_approval'):
                logger.info(f"Order {order_id} requires manual approval")
                return FulfillmentResult(
                    success=False,
                    order_id=order_id,
                    fulfillment_id=fulfillment_id,
                    status=FulfillmentStatus.EXCEPTION,
                    carrier=None,
                    tracking_number=None,
                    shipping_cost=None,
                    estimated_delivery=None,
                    label_url=None,
                    error_message="Order requires manual approval",
                    processing_time=(datetime.now() - start_time).total_seconds()
                )

            # Step 3: Allocate inventory
            inventory_success, allocation_details = self.inventory_allocator.allocate_inventory(context.order_items)
            if not inventory_success:
                error_msg = "Inventory allocation failed: " + str(allocation_details)
                logger.error(error_msg)
                return FulfillmentResult(
                    success=False,
                    order_id=order_id,
                    fulfillment_id=fulfillment_id,
                    status=FulfillmentStatus.EXCEPTION,
                    carrier=None,
                    tracking_number=None,
                    shipping_cost=None,
                    estimated_delivery=None,
                    label_url=None,
                    error_message=error_msg,
                    processing_time=(datetime.now() - start_time).total_seconds()
                )

            self.workflow_manager.update_fulfillment_status(order_id, FulfillmentStatus.INVENTORY_ALLOCATED)

            # Step 4: Select carrier and create shipment
            carrier_result = self._select_carrier_and_create_shipment(context, decisions)
            if not carrier_result['success']:
                # Release allocated inventory
                self.inventory_allocator.release_inventory(context.order_items)
                return FulfillmentResult(
                    success=False,
                    order_id=order_id,
                    fulfillment_id=fulfillment_id,
                    status=FulfillmentStatus.EXCEPTION,
                    carrier=None,
                    tracking_number=None,
                    shipping_cost=None,
                    estimated_delivery=None,
                    label_url=None,
                    error_message=carrier_result.get('error', 'Carrier selection failed'),
                    processing_time=(datetime.now() - start_time).total_seconds()
                )

            self.workflow_manager.update_fulfillment_status(order_id, FulfillmentStatus.CARRIER_SELECTED)
            self.workflow_manager.update_fulfillment_status(order_id, FulfillmentStatus.LABEL_CREATED)

            # Step 5: Schedule pickup if required
            if carrier_result.get('pickup_required', True):
                pickup_result = self._schedule_pickup(context, carrier_result)
                if pickup_result.get('success'):
                    self.workflow_manager.update_fulfillment_status(order_id, FulfillmentStatus.PICKUP_SCHEDULED)

            # Step 6: Mark as shipped
            self.workflow_manager.update_fulfillment_status(order_id, FulfillmentStatus.SHIPPED)

            # Commit all database changes
            self.db.commit()

            processing_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"Successfully processed order {order_id} in {processing_time:.2f} seconds")

            return FulfillmentResult(
                success=True,
                order_id=order_id,
                fulfillment_id=fulfillment_id,
                status=FulfillmentStatus.SHIPPED,
                carrier=ShippingCarrier(carrier_result['carrier']),
                tracking_number=carrier_result.get('tracking_number'),
                shipping_cost=carrier_result.get('shipping_cost'),
                estimated_delivery=carrier_result.get('estimated_delivery'),
                label_url=carrier_result.get('label_url'),
                error_message=None,
                processing_time=processing_time
            )

        except Exception as e:
            self.db.rollback()
            error_msg = f"Fulfillment processing failed: {str(e)}"
            logger.error(error_msg)

            # Try to release inventory if it was allocated
            try:
                if 'context' in locals():
                    self.inventory_allocator.release_inventory(context.order_items)
            except:
                pass

            return FulfillmentResult(
                success=False,
                order_id=order_id,
                fulfillment_id=fulfillment_id,
                status=FulfillmentStatus.EXCEPTION,
                carrier=None,
                tracking_number=None,
                shipping_cost=None,
                estimated_delivery=None,
                label_url=None,
                error_message=error_msg,
                processing_time=(datetime.now() - start_time).total_seconds()
            )

    def _create_fulfillment_context(self, order_id: int, priority: FulfillmentPriority) -> Optional[FulfillmentContext]:
        """Create fulfillment context from order data"""
        try:
            from app import Order, OrderItem, Product

            # Load order with items
            order = self.db.query(Order).options(
                joinedload(Order.order_items).joinedload(OrderItem.product)
            ).filter(Order.id == order_id).first()

            if not order:
                logger.error(f"Order {order_id} not found")
                return None

            # Create shipping address
            shipping_addr = order.shipping_address
            shipping_address = Address(
                name=shipping_addr.get('name', ''),
                company=shipping_addr.get('company'),
                address_line_1=shipping_addr.get('address_line_1', ''),
                address_line_2=shipping_addr.get('address_line_2'),
                city=shipping_addr.get('city', ''),
                state=shipping_addr.get('state', ''),
                postal_code=shipping_addr.get('postal_code', ''),
                country=shipping_addr.get('country', 'India'),
                phone=shipping_addr.get('phone'),
                email=shipping_addr.get('email')
            )

            # Create billing address if different
            billing_address = None
            if order.billing_address and order.billing_address != order.shipping_address:
                billing_addr = order.billing_address
                billing_address = Address(
                    name=billing_addr.get('name', ''),
                    company=billing_addr.get('company'),
                    address_line_1=billing_addr.get('address_line_1', ''),
                    address_line_2=billing_addr.get('address_line_2'),
                    city=billing_addr.get('city', ''),
                    state=billing_addr.get('state', ''),
                    postal_code=billing_addr.get('postal_code', ''),
                    country=billing_addr.get('country', 'India'),
                    phone=billing_addr.get('phone'),
                    email=billing_addr.get('email')
                )

            # Prepare order items data
            order_items = []
            total_weight = 0.0

            for order_item in order.order_items:
                if not order_item.product:
                    continue

                # Estimate weight (can be enhanced with actual product weights)
                item_weight = 0.5 * order_item.quantity  # 0.5kg per item as default
                total_weight += item_weight

                order_items.append({
                    'product_id': order_item.product_id,
                    'quantity': order_item.quantity,
                    'unit_price': order_item.unit_price,
                    'total_price': order_item.total_price,
                    'product_name': order_item.product_name,
                    'weight': item_weight
                })

            # Determine customer type
            customer_type = 'guest' if order.is_guest_order else 'registered'

            # Check if international shipping
            is_international = shipping_address.country.lower() != 'india'

            # Create context
            context = FulfillmentContext(
                order_id=order_id,
                order_total=order.total_amount,
                order_weight=total_weight,
                order_items=order_items,
                shipping_address=shipping_address,
                billing_address=billing_address,
                customer_type=customer_type,
                shipping_method='standard',  # Can be enhanced
                special_instructions=order.order_notes,
                is_international=is_international,
                requires_signature=False,  # Will be determined by rules
                requires_insurance=False,  # Will be determined by rules
                priority=priority
            )

            return context

        except Exception as e:
            logger.error(f"Error creating fulfillment context: {e}")
            return None

    def _select_carrier_and_create_shipment(self, context: FulfillmentContext,
                                          decisions: Dict[str, Any]) -> Dict[str, Any]:
        """Select optimal carrier and create shipment"""
        try:
            # Create origin address (warehouse/fulfillment center)
            origin = Address(
                name="Allora Fulfillment Center",
                company="Allora",
                address_line_1="123 Warehouse Street",
                address_line_2=None,
                city="Mumbai",
                state="Maharashtra",
                postal_code="400001",
                country="India",
                phone="+91-9876543210",
                email="<EMAIL>"
            )

            # Create packages
            packages = []
            for item in context.order_items:
                # Simple packaging logic (can be enhanced)
                package = Package(
                    weight=item['weight'],
                    length=20.0,  # cm
                    width=15.0,   # cm
                    height=10.0,  # cm
                    declared_value=item['total_price']
                )
                packages.append(package)

            # Get preferred carrier from decisions
            preferred_carrier = decisions.get('preferred_carrier')
            carrier = None
            if preferred_carrier:
                try:
                    carrier = ShippingCarrier(preferred_carrier)
                except ValueError:
                    logger.warning(f"Invalid preferred carrier: {preferred_carrier}")

            # Create shipment
            result = self.shipment_manager.create_shipment(
                origin=origin,
                destination=context.shipping_address,
                packages=packages,
                carrier=carrier,
                service_type='Standard',
                reference_number=f"ORD-{context.order_id}"
            )

            if result.get('success'):
                # Save shipment to database
                self._save_shipment_to_database(context.order_id, result, carrier or ShippingCarrier.BLUE_DART)

            return result

        except Exception as e:
            logger.error(f"Error selecting carrier and creating shipment: {e}")
            return {'success': False, 'error': str(e)}

    def _save_shipment_to_database(self, order_id: int, shipment_result: Dict[str, Any],
                                  carrier: ShippingCarrier):
        """Save shipment information to database"""
        try:
            from app import Shipment, ShippingCarrier as CarrierModel

            # Get or create carrier record
            carrier_record = self.db.query(CarrierModel).filter_by(code=carrier.value).first()
            if not carrier_record:
                carrier_record = CarrierModel(
                    name=carrier.value.title(),
                    code=carrier.value,
                    is_active=True
                )
                self.db.add(carrier_record)
                self.db.flush()

            # Create shipment record
            shipment = Shipment(
                order_id=order_id,
                carrier_id=carrier_record.id,
                tracking_number=shipment_result.get('tracking_number'),
                service_type='Standard',
                status='label_created',
                shipping_cost=shipment_result.get('shipping_cost', 0),
                estimated_delivery_date=shipment_result.get('estimated_delivery'),
                label_url=shipment_result.get('label_url'),
                carrier_reference=shipment_result.get('carrier_reference')
            )

            self.db.add(shipment)
            logger.info(f"Saved shipment record for order {order_id}")

        except Exception as e:
            logger.error(f"Error saving shipment to database: {e}")

    def _schedule_pickup(self, context: FulfillmentContext, carrier_result: Dict[str, Any]) -> Dict[str, Any]:
        """Schedule pickup with carrier"""
        try:
            # Create pickup address (same as origin)
            pickup_address = Address(
                name="Allora Fulfillment Center",
                company="Allora",
                address_line_1="123 Warehouse Street",
                address_line_2=None,
                city="Mumbai",
                state="Maharashtra",
                postal_code="400001",
                country="India",
                phone="+91-9876543210",
                email="<EMAIL>"
            )

            # Create packages for pickup
            packages = []
            for item in context.order_items:
                package = Package(
                    weight=item['weight'],
                    length=20.0,
                    width=15.0,
                    height=10.0,
                    declared_value=item['total_price']
                )
                packages.append(package)

            # Schedule pickup for next business day
            pickup_date = datetime.now() + timedelta(days=1)
            carrier = ShippingCarrier(carrier_result['carrier'])

            result = self.shipment_manager.schedule_pickup(
                pickup_address=pickup_address,
                packages=packages,
                pickup_date=pickup_date,
                carrier=carrier,
                time_window='10:00-18:00'
            )

            return result

        except Exception as e:
            logger.error(f"Error scheduling pickup: {e}")
            return {'success': False, 'error': str(e)}

    def process_orders_batch(self, order_ids: List[int],
                           priority: FulfillmentPriority = FulfillmentPriority.NORMAL) -> List[FulfillmentResult]:
        """Process multiple orders in batch"""
        results = []

        logger.info(f"Starting batch processing for {len(order_ids)} orders")

        # Process orders concurrently
        futures = []
        for order_id in order_ids:
            future = self.executor.submit(self.process_order, order_id, priority)
            futures.append((order_id, future))

        # Collect results
        for order_id, future in futures:
            try:
                result = future.result(timeout=300)  # 5 minute timeout per order
                results.append(result)
                logger.info(f"Batch processing completed for order {order_id}: {result.success}")
            except Exception as e:
                logger.error(f"Batch processing failed for order {order_id}: {e}")
                results.append(FulfillmentResult(
                    success=False,
                    order_id=order_id,
                    fulfillment_id=f"FUL-{uuid.uuid4().hex[:8].upper()}",
                    status=FulfillmentStatus.EXCEPTION,
                    carrier=None,
                    tracking_number=None,
                    shipping_cost=None,
                    estimated_delivery=None,
                    label_url=None,
                    error_message=str(e),
                    processing_time=0.0
                ))

        logger.info(f"Batch processing completed. Success: {sum(1 for r in results if r.success)}/{len(results)}")
        return results

    def get_fulfillment_status(self, order_id: int) -> Optional[FulfillmentStatus]:
        """Get current fulfillment status for an order"""
        try:
            from app import Order

            order = self.db.query(Order).filter(Order.id == order_id).first()
            if not order:
                return None

            # Map order status to fulfillment status
            status_mapping = {
                'pending': FulfillmentStatus.PENDING,
                'processing': FulfillmentStatus.PROCESSING,
                'confirmed': FulfillmentStatus.INVENTORY_ALLOCATED,
                'shipped': FulfillmentStatus.SHIPPED,
                'delivered': FulfillmentStatus.DELIVERED,
                'cancelled': FulfillmentStatus.CANCELLED
            }

            return status_mapping.get(order.status, FulfillmentStatus.PENDING)

        except Exception as e:
            logger.error(f"Error getting fulfillment status: {e}")
            return None

    def cancel_fulfillment(self, order_id: int, reason: str = "Customer request") -> bool:
        """Cancel order fulfillment and release inventory"""
        try:
            logger.info(f"Cancelling fulfillment for order {order_id}: {reason}")

            # Get order context
            context = self._create_fulfillment_context(order_id, FulfillmentPriority.HIGH)
            if not context:
                return False

            # Release allocated inventory
            self.inventory_allocator.release_inventory(context.order_items)

            # Update status
            self.workflow_manager.update_fulfillment_status(order_id, FulfillmentStatus.CANCELLED)

            # Cancel shipment if it exists
            try:
                from app import Shipment
                shipment = self.db.query(Shipment).filter(Shipment.order_id == order_id).first()
                if shipment and shipment.tracking_number:
                    # Cancel with carrier (implementation depends on carrier API)
                    logger.info(f"Shipment cancellation may be required for tracking: {shipment.tracking_number}")
            except Exception as e:
                logger.warning(f"Could not cancel shipment: {e}")

            self.db.commit()
            logger.info(f"Successfully cancelled fulfillment for order {order_id}")
            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error cancelling fulfillment: {e}")
            return False

    def retry_failed_fulfillment(self, order_id: int) -> FulfillmentResult:
        """Retry fulfillment for a failed order"""
        logger.info(f"Retrying fulfillment for order {order_id}")

        # Reset status to processing
        self.workflow_manager.update_fulfillment_status(order_id, FulfillmentStatus.PROCESSING)

        # Process with high priority
        return self.process_order(order_id, FulfillmentPriority.HIGH)

    def get_fulfillment_metrics(self) -> Dict[str, Any]:
        """Get fulfillment performance metrics"""
        try:
            from app import Order, Shipment

            # Get metrics from database
            total_orders = self.db.query(Order).count()
            shipped_orders = self.db.query(Order).filter(Order.status == 'shipped').count()
            delivered_orders = self.db.query(Order).filter(Order.status == 'delivered').count()
            cancelled_orders = self.db.query(Order).filter(Order.status == 'cancelled').count()

            # Calculate rates
            fulfillment_rate = (shipped_orders / total_orders * 100) if total_orders > 0 else 0
            delivery_rate = (delivered_orders / total_orders * 100) if total_orders > 0 else 0
            cancellation_rate = (cancelled_orders / total_orders * 100) if total_orders > 0 else 0

            return {
                'total_orders': total_orders,
                'shipped_orders': shipped_orders,
                'delivered_orders': delivered_orders,
                'cancelled_orders': cancelled_orders,
                'fulfillment_rate': round(fulfillment_rate, 2),
                'delivery_rate': round(delivery_rate, 2),
                'cancellation_rate': round(cancellation_rate, 2),
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting fulfillment metrics: {e}")
            return {}

# ============================================================================
# FULFILLMENT ENGINE FACTORY AND UTILITIES
# ============================================================================

class FulfillmentEngineFactory:
    """Factory for creating fulfillment engine instances"""

    _instance = None
    _lock = threading.Lock()

    @classmethod
    def get_engine(cls, db_session):
        """Get singleton fulfillment engine instance"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = OrderFulfillmentEngine(db_session)
        return cls._instance

def create_fulfillment_engine(db_session) -> OrderFulfillmentEngine:
    """Create a new fulfillment engine instance"""
    return OrderFulfillmentEngine(db_session)

def process_order_fulfillment(order_id: int, db_session,
                            priority: FulfillmentPriority = FulfillmentPriority.NORMAL) -> FulfillmentResult:
    """Convenience function to process a single order"""
    engine = create_fulfillment_engine(db_session)
    return engine.process_order(order_id, priority)

# ============================================================================
# INTEGRATION HOOKS
# ============================================================================

def integrate_with_checkout(order_id: int, db_session):
    """
    Integration hook to be called after order creation in checkout process
    This can be called from the checkout endpoint to automatically start fulfillment
    """
    try:
        logger.info(f"Starting automatic fulfillment for new order {order_id}")

        # Process order with normal priority
        result = process_order_fulfillment(order_id, db_session, FulfillmentPriority.NORMAL)

        if result.success:
            logger.info(f"Automatic fulfillment successful for order {order_id}")
        else:
            logger.error(f"Automatic fulfillment failed for order {order_id}: {result.error_message}")

        return result

    except Exception as e:
        logger.error(f"Error in checkout integration: {e}")
        return None

if __name__ == "__main__":
    # Example usage and testing
    print("Order Fulfillment Engine - Test Mode")

    # This would typically be called with actual database session
    # engine = create_fulfillment_engine(db_session)
    # result = engine.process_order(order_id=123)
    # print(f"Fulfillment result: {result.to_dict()}")

    print("Fulfillment engine ready for integration")
