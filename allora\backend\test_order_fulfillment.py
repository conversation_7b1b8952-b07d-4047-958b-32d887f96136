"""
Comprehensive Order Fulfillment System Test
==========================================

Tests all components of the order fulfillment system including:
1. Order Fulfillment Engine
2. Carrier Integration
3. Fulfillment API
4. Inventory Allocation
5. Business Rules Engine
6. Workflow Management
7. Database Integration
"""

import sys
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, Any

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_fulfillment_imports():
    """Test if all fulfillment modules can be imported"""
    print("\n" + "="*70)
    print("🧪 TESTING ORDER FULFILLMENT IMPORTS")
    print("="*70)
    
    try:
        # Test core fulfillment engine
        from order_fulfillment.order_fulfillment_engine import (
            OrderFulfillmentEngine, FulfillmentStatus, FulfillmentPriority,
            create_fulfillment_engine, process_order_fulfillment
        )
        print("✅ order_fulfillment_engine.py imports successful")

        # Test carrier integration
        from order_fulfillment.carrier_integration import (
            CarrierFactory, RateCalculationEngine, ShipmentManager,
            ShippingCarrier, CarrierAPIError
        )
        print("✅ carrier_integration.py imports successful")

        # Test fulfillment API
        from order_fulfillment.fulfillment_api import fulfillment_bp
        print("✅ fulfillment_api.py imports successful")

        # Test architecture components
        from order_fulfillment.order_fulfillment_architecture import (
            Address, Package, FulfillmentRequest, TrackingEventType
        )
        print("✅ order_fulfillment_architecture.py imports successful")

        # Test inventory allocator
        from order_fulfillment.inventory_allocator import (
            AdvancedInventoryAllocator, AllocationRequest, AllocationResult
        )
        print("✅ inventory_allocator.py imports successful")

        # Test fulfillment rules
        from order_fulfillment.fulfillment_rules import (
            FulfillmentRuleManager, DefaultFulfillmentRules, BusinessRuleType
        )
        print("✅ fulfillment_rules.py imports successful")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_database_models():
    """Test fulfillment-related database models"""
    print("\n" + "="*70)
    print("🧪 TESTING FULFILLMENT DATABASE MODELS")
    print("="*70)
    
    try:
        from app import db, ShippingCarrier, Shipment, TrackingEvent, FulfillmentRule
        
        # Check ShippingCarrier model
        carrier_attrs = ['id', 'name', 'code', 'api_endpoint', 'is_active']
        for attr in carrier_attrs:
            if hasattr(ShippingCarrier, attr):
                print(f"✅ ShippingCarrier.{attr} exists")
            else:
                print(f"❌ ShippingCarrier.{attr} missing")
        
        # Check Shipment model
        shipment_attrs = ['id', 'order_id', 'carrier_id', 'tracking_number', 'status']
        for attr in shipment_attrs:
            if hasattr(Shipment, attr):
                print(f"✅ Shipment.{attr} exists")
            else:
                print(f"❌ Shipment.{attr} missing")
        
        # Check FulfillmentRule model
        rule_attrs = ['id', 'name', 'description', 'conditions', 'actions']
        for attr in rule_attrs:
            if hasattr(FulfillmentRule, attr):
                print(f"✅ FulfillmentRule.{attr} exists")
            else:
                print(f"❌ FulfillmentRule.{attr} missing")
        
        print("✅ Database models verification complete")
        return True
        
    except ImportError as e:
        print(f"❌ Model import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Model verification error: {e}")
        return False

def test_fulfillment_engine():
    """Test order fulfillment engine functionality"""
    print("\n" + "="*70)
    print("🧪 TESTING ORDER FULFILLMENT ENGINE")
    print("="*70)
    
    try:
        from app import app, db
        from order_fulfillment.order_fulfillment_engine import create_fulfillment_engine, FulfillmentStatus
        
        with app.app_context():
            # Create fulfillment engine instance
            engine = create_fulfillment_engine(db.session)
            print("✅ Fulfillment engine instance created")

            # Test engine methods
            methods = [
                'process_order',
                'get_fulfillment_status',
                'cancel_fulfillment',
                'retry_failed_fulfillment',
                'process_orders_batch',
                'get_fulfillment_metrics'
            ]
            
            for method_name in methods:
                if hasattr(engine, method_name):
                    print(f"✅ Method available: {method_name}")
                else:
                    print(f"❌ Method missing: {method_name}")
            
            # Test fulfillment metrics
            try:
                metrics = engine.get_fulfillment_metrics()
                print(f"✅ Fulfillment metrics: {type(metrics).__name__}")
                if isinstance(metrics, dict):
                    print(f"   - Metrics keys: {list(metrics.keys())}")
            except Exception as e:
                print(f"⚠️ Metrics test failed: {e}")
            
        return True
        
    except Exception as e:
        print(f"❌ Fulfillment engine test error: {e}")
        return False

def test_carrier_integration():
    """Test carrier integration system"""
    print("\n" + "="*70)
    print("🧪 TESTING CARRIER INTEGRATION")
    print("="*70)
    
    try:
        from order_fulfillment.carrier_integration import CarrierFactory, ShippingCarrier, RateCalculationEngine
        
        # Test carrier factory
        factory = CarrierFactory()
        print("✅ CarrierFactory instance created")
        
        # Test supported carriers
        carriers = [ShippingCarrier.BLUE_DART, ShippingCarrier.DELHIVERY, ShippingCarrier.FEDEX]
        for carrier in carriers:
            try:
                carrier_api = factory.get_carrier(carrier)
                print(f"✅ {carrier.value} API instance created")
            except Exception as e:
                print(f"⚠️ {carrier.value} API creation failed: {e}")
        
        # Test rate calculation engine
        rate_engine = RateCalculationEngine()
        print("✅ Rate calculation engine created")
        
        # Test rate engine methods
        rate_methods = ['calculate_rates', 'get_best_rate', 'compare_carriers']
        for method_name in rate_methods:
            if hasattr(rate_engine, method_name):
                print(f"✅ Rate method available: {method_name}")
            else:
                print(f"❌ Rate method missing: {method_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Carrier integration test error: {e}")
        return False

def test_inventory_allocation():
    """Test inventory allocation system"""
    print("\n" + "="*70)
    print("🧪 TESTING INVENTORY ALLOCATION")
    print("="*70)
    
    try:
        from app import app, db
        from order_fulfillment.inventory_allocator import AdvancedInventoryAllocator, AllocationRequest
        
        with app.app_context():
            # Create inventory allocator
            allocator = AdvancedInventoryAllocator(db.session)
            print("✅ Inventory allocator instance created")
            
            # Test allocator methods
            allocation_methods = [
                'reserve_inventory',
                'confirm_allocation',
                'release_reservation',
                'get_available_quantity',
                'check_availability'
            ]
            
            for method_name in allocation_methods:
                if hasattr(allocator, method_name):
                    print(f"✅ Allocation method available: {method_name}")
                else:
                    print(f"❌ Allocation method missing: {method_name}")
            
        return True
        
    except Exception as e:
        print(f"❌ Inventory allocation test error: {e}")
        return False

def test_business_rules():
    """Test business rules engine"""
    print("\n" + "="*70)
    print("🧪 TESTING BUSINESS RULES ENGINE")
    print("="*70)
    
    try:
        from order_fulfillment.fulfillment_rules import FulfillmentRuleManager, DefaultFulfillmentRules, BusinessRuleType
        
        # Test rule manager
        rule_manager = FulfillmentRuleManager()
        print("✅ Rule manager instance created")
        print(f"✅ Loaded {len(rule_manager.rules)} default rules")
        
        # Test default rules
        default_rules = DefaultFulfillmentRules()
        
        rule_types = [
            ('Auto Fulfill Rules', default_rules.get_auto_fulfill_rules),
            ('Approval Rules', default_rules.get_approval_rules),
            ('Carrier Selection Rules', default_rules.get_carrier_selection_rules),
            ('Priority Rules', default_rules.get_priority_rules)
        ]
        
        for rule_name, rule_method in rule_types:
            try:
                rules = rule_method()
                print(f"✅ {rule_name}: {len(rules)} rules")
            except Exception as e:
                print(f"⚠️ {rule_name} failed: {e}")
        
        # Test business rule types
        rule_types_enum = [
            BusinessRuleType.AUTO_FULFILL,
            BusinessRuleType.REQUIRE_APPROVAL,
            BusinessRuleType.CARRIER_SELECTION,
            BusinessRuleType.PRIORITY
        ]
        
        print("✅ Business rule types:")
        for rule_type in rule_types_enum:
            print(f"   - {rule_type.name}: {rule_type.value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Business rules test error: {e}")
        return False

def test_api_endpoints():
    """Test fulfillment API endpoints"""
    print("\n" + "="*70)
    print("🧪 TESTING FULFILLMENT API ENDPOINTS")
    print("="*70)
    
    try:
        from app import app
        
        # Check if fulfillment blueprint is registered
        blueprint_names = [bp.name for bp in app.blueprints.values()]
        
        if 'fulfillment' in blueprint_names:
            print("✅ fulfillment blueprint registered")
        else:
            print("❌ fulfillment blueprint NOT registered")
        
        # Test endpoint availability
        fulfillment_routes = []
        for rule in app.url_map.iter_rules():
            if 'fulfillment' in rule.rule:
                fulfillment_routes.append(rule.rule)
        
        print(f"✅ Found {len(fulfillment_routes)} fulfillment routes:")
        for route in fulfillment_routes[:10]:  # Show first 10
            print(f"   - {route}")
        
        # Test basic endpoint access
        try:
            with app.test_client() as client:
                # Test fulfillment metrics endpoint
                response = client.get('/api/fulfillment/metrics')
                print(f"✅ Metrics endpoint: Status {response.status_code}")
                
                # Test fulfillment status endpoint (will fail without valid order)
                response = client.get('/api/fulfillment/status/999999')
                print(f"✅ Status endpoint: Status {response.status_code}")
                
        except Exception as client_error:
            print(f"⚠️ Client testing limited due to: {client_error}")
            print("✅ Endpoints exist but full testing requires server context")
        
        return True
        
    except Exception as e:
        print(f"❌ API endpoints test error: {e}")
        return False

def test_integration_points():
    """Test integration with other systems"""
    print("\n" + "="*70)
    print("🧪 TESTING SYSTEM INTEGRATIONS")
    print("="*70)
    
    try:
        # Test tracking system integration
        from tracking_system import get_tracking_system
        print("✅ Tracking system integration available")
        
        # Test notification service integration
        from notification_service import get_notification_service
        print("✅ Notification service integration available")
        
        # Test checkout integration
        from order_fulfillment.order_fulfillment_engine import integrate_with_checkout
        print("✅ Checkout integration hook available")
        
        # Test Flask-SocketIO integration
        from flask_socketio_manager import send_order_status_update
        print("✅ Real-time notification integration available")
        
        return True
        
    except ImportError as e:
        print(f"❌ Integration import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Integration test error: {e}")
        return False

def run_comprehensive_fulfillment_test():
    """Run all fulfillment system tests"""
    print("🚀 STARTING COMPREHENSIVE ORDER FULFILLMENT TESTS")
    print("="*80)
    
    test_results = []
    
    # Run all tests
    test_results.append(("Import Tests", test_fulfillment_imports()))
    test_results.append(("Database Models", test_database_models()))
    test_results.append(("Fulfillment Engine", test_fulfillment_engine()))
    test_results.append(("Carrier Integration", test_carrier_integration()))
    test_results.append(("Inventory Allocation", test_inventory_allocation()))
    test_results.append(("Business Rules", test_business_rules()))
    test_results.append(("API Endpoints", test_api_endpoints()))
    test_results.append(("System Integrations", test_integration_points()))
    
    # Print summary
    print("\n" + "="*80)
    print("📊 ORDER FULFILLMENT TEST RESULTS SUMMARY")
    print("="*80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 OVERALL RESULT: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Order fulfillment system is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the details above.")
    
    return passed == total

if __name__ == "__main__":
    success = run_comprehensive_fulfillment_test()
    sys.exit(0 if success else 1)
