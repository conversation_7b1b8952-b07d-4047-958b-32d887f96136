2025-07-13 10:16:09 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 10:16:09 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 10:16:09 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 10:16:09 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 10:16:09 - startup - INFO - ============================================================
2025-07-13 10:16:09 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 10:16:09 - startup - INFO - ============================================================
2025-07-13 10:16:09 - startup - INFO - 📅 Startup Time: 2025-07-13T10:16:09.588100
2025-07-13 10:16:09 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 10:16:09 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 10:16:09 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 10:16:09 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 10:16:09 - startup - INFO - ============================================================
2025-07-13 10:16:09 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 10:16:09 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.009s]
2025-07-13 10:16:09 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 10:16:10 - allora - INFO - Search API blueprint registered successfully
2025-07-13 10:16:10 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 10:16:10 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 10:16:10 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 10:16:10 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 10:16:10 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 10:16:10 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 10:16:10 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 10:16:10 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 10:16:10 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 10:16:10 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 10:16:10 - allora - INFO - Recommendation system initialized successfully
2025-07-13 10:16:10 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 10:16:10 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 10:16:10 - tracking_system - INFO - Real-time tracking system started
2025-07-13 10:16:10 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 10:16:10 - notification_service - INFO - Notification delivery service started
2025-07-13 10:16:10 - allora - INFO - Notification service initialized successfully
2025-07-13 10:16:10 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:20:42 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 10:20:42 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 10:20:42 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 10:20:42 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 10:20:42 - startup - INFO - ============================================================
2025-07-13 10:20:42 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 10:20:42 - startup - INFO - ============================================================
2025-07-13 10:20:42 - startup - INFO - 📅 Startup Time: 2025-07-13T10:20:42.293987
2025-07-13 10:20:42 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 10:20:42 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 10:20:42 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 10:20:42 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 10:20:42 - startup - INFO - ============================================================
2025-07-13 10:20:42 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 10:20:42 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-13 10:20:42 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 10:20:42 - allora - INFO - Search API blueprint registered successfully
2025-07-13 10:20:42 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 10:20:43 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 10:20:43 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 10:20:43 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 10:20:43 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 10:20:43 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 10:20:43 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 10:20:43 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 10:20:43 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 10:20:43 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 10:20:43 - allora - INFO - Recommendation system initialized successfully
2025-07-13 10:20:43 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 10:20:43 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 10:20:43 - tracking_system - INFO - Real-time tracking system started
2025-07-13 10:20:43 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 10:20:43 - notification_service - INFO - Notification delivery service started
2025-07-13 10:20:43 - allora - INFO - Notification service initialized successfully
2025-07-13 10:20:43 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:30:26 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 10:30:26 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 10:30:26 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 10:30:26 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 10:30:26 - startup - INFO - ============================================================
2025-07-13 10:30:26 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 10:30:26 - startup - INFO - ============================================================
2025-07-13 10:30:26 - startup - INFO - 📅 Startup Time: 2025-07-13T10:30:26.730480
2025-07-13 10:30:26 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 10:30:26 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 10:30:26 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 10:30:26 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 10:30:26 - startup - INFO - ============================================================
2025-07-13 10:30:26 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 10:30:27 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-13 10:30:27 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 10:30:27 - allora - INFO - Search API blueprint registered successfully
2025-07-13 10:30:27 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 10:30:27 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 10:30:27 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 10:30:27 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 10:30:27 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 10:30:27 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 10:30:27 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 10:30:27 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 10:30:27 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 10:30:27 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 10:30:27 - allora - INFO - Recommendation system initialized successfully
2025-07-13 10:30:27 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 10:30:27 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 10:30:27 - tracking_system - INFO - Real-time tracking system started
2025-07-13 10:30:27 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 10:30:27 - notification_service - INFO - Notification delivery service started
2025-07-13 10:30:27 - allora - INFO - Notification service initialized successfully
2025-07-13 10:30:27 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:33:40 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 10:33:40 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 10:33:40 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 10:33:40 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 10:33:40 - startup - INFO - ============================================================
2025-07-13 10:33:40 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 10:33:40 - startup - INFO - ============================================================
2025-07-13 10:33:40 - startup - INFO - 📅 Startup Time: 2025-07-13T10:33:40.762640
2025-07-13 10:33:40 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 10:33:40 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 10:33:40 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 10:33:40 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 10:33:40 - startup - INFO - ============================================================
2025-07-13 10:33:40 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 10:33:41 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-13 10:33:41 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 10:33:41 - allora - INFO - Search API blueprint registered successfully
2025-07-13 10:33:41 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 10:33:41 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 10:33:41 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 10:33:41 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 10:33:41 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 10:33:41 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 10:33:41 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 10:33:41 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 10:33:41 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 10:33:41 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 10:33:41 - allora - INFO - Recommendation system initialized successfully
2025-07-13 10:33:41 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 10:33:41 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 10:33:41 - tracking_system - INFO - Real-time tracking system started
2025-07-13 10:33:41 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 10:33:41 - notification_service - INFO - Notification delivery service started
2025-07-13 10:33:41 - allora - INFO - Notification service initialized successfully
2025-07-13 10:33:41 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:34:12 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 10:34:12 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 10:34:12 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 10:34:12 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 10:34:12 - startup - INFO - ============================================================
2025-07-13 10:34:12 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 10:34:12 - startup - INFO - ============================================================
2025-07-13 10:34:12 - startup - INFO - 📅 Startup Time: 2025-07-13T10:34:12.516263
2025-07-13 10:34:12 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 10:34:12 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 10:34:12 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 10:34:12 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 10:34:12 - startup - INFO - ============================================================
2025-07-13 10:34:12 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 10:34:12 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-13 10:34:12 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 10:34:13 - allora - INFO - Search API blueprint registered successfully
2025-07-13 10:34:13 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 10:34:13 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 10:34:13 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 10:34:13 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 10:34:13 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 10:34:13 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 10:34:13 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 10:34:13 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 10:34:13 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 10:34:13 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 10:34:13 - allora - INFO - Recommendation system initialized successfully
2025-07-13 10:34:13 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 10:34:13 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 10:34:13 - tracking_system - INFO - Real-time tracking system started
2025-07-13 10:34:13 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 10:34:13 - notification_service - INFO - Notification delivery service started
2025-07-13 10:34:13 - allora - INFO - Notification service initialized successfully
2025-07-13 10:34:13 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:37:08 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 10:37:08 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 10:37:08 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 10:37:08 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 10:37:08 - startup - INFO - ============================================================
2025-07-13 10:37:08 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 10:37:08 - startup - INFO - ============================================================
2025-07-13 10:37:08 - startup - INFO - 📅 Startup Time: 2025-07-13T10:37:08.658094
2025-07-13 10:37:08 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 10:37:08 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 10:37:08 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 10:37:08 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 10:37:08 - startup - INFO - ============================================================
2025-07-13 10:37:08 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 10:37:09 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-13 10:37:09 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 10:37:09 - allora - INFO - Search API blueprint registered successfully
2025-07-13 10:37:09 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 10:37:09 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 10:37:09 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 10:37:09 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 10:37:09 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 10:37:09 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 10:37:09 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 10:37:09 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 10:37:09 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 10:37:09 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 10:37:09 - allora - INFO - Recommendation system initialized successfully
2025-07-13 10:37:09 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 10:37:09 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 10:37:09 - tracking_system - INFO - Real-time tracking system started
2025-07-13 10:37:09 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 10:37:09 - notification_service - INFO - Notification delivery service started
2025-07-13 10:37:09 - allora - INFO - Notification service initialized successfully
2025-07-13 10:37:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:37:45 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 10:37:45 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 10:37:45 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 10:37:45 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 10:37:45 - startup - INFO - ============================================================
2025-07-13 10:37:45 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 10:37:45 - startup - INFO - ============================================================
2025-07-13 10:37:45 - startup - INFO - 📅 Startup Time: 2025-07-13T10:37:45.483857
2025-07-13 10:37:45 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 10:37:45 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 10:37:45 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 10:37:45 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 10:37:45 - startup - INFO - ============================================================
2025-07-13 10:37:45 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 10:37:45 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-13 10:37:45 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 10:37:45 - allora - INFO - Search API blueprint registered successfully
2025-07-13 10:37:45 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 10:37:46 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 10:37:46 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 10:37:46 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 10:37:46 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 10:37:46 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 10:37:46 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 10:37:46 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 10:37:46 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 10:37:46 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 10:37:46 - allora - INFO - Recommendation system initialized successfully
2025-07-13 10:37:46 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 10:37:46 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 10:37:46 - tracking_system - INFO - Real-time tracking system started
2025-07-13 10:37:46 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 10:37:46 - notification_service - INFO - Notification delivery service started
2025-07-13 10:37:46 - allora - INFO - Notification service initialized successfully
2025-07-13 10:37:46 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:41:45 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 10:41:45 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 10:41:45 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 10:41:45 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 10:41:45 - startup - INFO - ============================================================
2025-07-13 10:41:45 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 10:41:45 - startup - INFO - ============================================================
2025-07-13 10:41:45 - startup - INFO - 📅 Startup Time: 2025-07-13T10:41:45.507218
2025-07-13 10:41:45 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 10:41:45 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 10:41:45 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 10:41:45 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 10:41:45 - startup - INFO - ============================================================
2025-07-13 10:41:45 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 10:41:45 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-13 10:41:45 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 10:41:45 - allora - INFO - Search API blueprint registered successfully
2025-07-13 10:41:45 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 10:41:46 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 10:41:46 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 10:41:46 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 10:41:46 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 10:41:46 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 10:41:46 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 10:41:46 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 10:41:46 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 10:41:46 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 10:41:46 - allora - INFO - Recommendation system initialized successfully
2025-07-13 10:41:46 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 10:41:46 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 10:41:46 - tracking_system - INFO - Real-time tracking system started
2025-07-13 10:41:46 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 10:41:46 - notification_service - INFO - Notification delivery service started
2025-07-13 10:41:46 - allora - INFO - Notification service initialized successfully
2025-07-13 10:41:46 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:49:56 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 10:49:56 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 10:49:56 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 10:49:56 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 10:49:56 - startup - INFO - ============================================================
2025-07-13 10:49:56 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 10:49:56 - startup - INFO - ============================================================
2025-07-13 10:49:56 - startup - INFO - 📅 Startup Time: 2025-07-13T10:49:56.837879
2025-07-13 10:49:56 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 10:49:56 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 10:49:56 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 10:49:56 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 10:49:56 - startup - INFO - ============================================================
2025-07-13 10:49:56 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 10:49:57 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-13 10:49:57 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 10:49:57 - allora - INFO - Search API blueprint registered successfully
2025-07-13 10:49:57 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 10:49:57 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 10:49:57 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 10:49:57 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 10:49:57 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 10:49:57 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 10:49:57 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 10:49:57 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 10:49:57 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 10:49:57 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 10:49:57 - allora - INFO - Recommendation system initialized successfully
2025-07-13 10:49:57 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 10:49:57 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 10:49:57 - tracking_system - INFO - Real-time tracking system started
2025-07-13 10:49:57 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 10:49:57 - notification_service - INFO - Notification delivery service started
2025-07-13 10:49:57 - allora - INFO - Notification service initialized successfully
2025-07-13 10:49:57 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:56:33 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 10:56:33 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 10:56:33 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 10:56:33 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 10:56:33 - startup - INFO - ============================================================
2025-07-13 10:56:33 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 10:56:33 - startup - INFO - ============================================================
2025-07-13 10:56:33 - startup - INFO - 📅 Startup Time: 2025-07-13T10:56:33.586153
2025-07-13 10:56:33 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 10:56:33 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 10:56:33 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 10:56:33 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 10:56:33 - startup - INFO - ============================================================
2025-07-13 10:56:33 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 10:56:33 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-13 10:56:33 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 10:56:33 - allora - INFO - Search API blueprint registered successfully
2025-07-13 10:56:34 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 10:56:34 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 10:56:34 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 10:56:34 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 10:56:34 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 10:56:34 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 10:56:34 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 10:56:34 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 10:56:34 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 10:56:34 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 10:56:34 - allora - INFO - Recommendation system initialized successfully
2025-07-13 10:56:34 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 10:56:34 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 10:56:34 - tracking_system - INFO - Real-time tracking system started
2025-07-13 10:56:34 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 10:56:34 - notification_service - INFO - Notification delivery service started
2025-07-13 10:56:34 - allora - INFO - Notification service initialized successfully
2025-07-13 10:56:34 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:00:30 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:00:30 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:00:30 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:00:30 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:00:30 - startup - INFO - ============================================================
2025-07-13 11:00:30 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:00:30 - startup - INFO - ============================================================
2025-07-13 11:00:30 - startup - INFO - 📅 Startup Time: 2025-07-13T11:00:30.757210
2025-07-13 11:00:30 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:00:30 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:00:30 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:00:30 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:00:30 - startup - INFO - ============================================================
2025-07-13 11:00:30 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:00:31 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-13 11:00:31 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:00:31 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:00:31 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:00:31 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:00:31 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:00:31 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:00:31 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:00:31 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:00:31 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:00:31 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:00:31 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:00:31 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:00:31 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:00:31 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:00:31 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:00:31 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:00:31 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:00:31 - notification_service - INFO - Notification delivery service started
2025-07-13 11:00:31 - allora - INFO - Notification service initialized successfully
2025-07-13 11:00:31 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:03:49 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:03:49 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:03:49 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:03:49 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:03:49 - startup - INFO - ============================================================
2025-07-13 11:03:49 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:03:49 - startup - INFO - ============================================================
2025-07-13 11:03:49 - startup - INFO - 📅 Startup Time: 2025-07-13T11:03:49.657566
2025-07-13 11:03:49 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:03:49 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:03:49 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:03:49 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:03:49 - startup - INFO - ============================================================
2025-07-13 11:03:49 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:03:50 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-13 11:03:50 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:03:50 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:03:50 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:03:50 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:03:50 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:03:50 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:03:50 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:03:50 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:03:50 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:03:50 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:03:50 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:03:50 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:03:50 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:03:50 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:03:50 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:03:50 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:03:50 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:03:50 - notification_service - INFO - Notification delivery service started
2025-07-13 11:03:50 - allora - INFO - Notification service initialized successfully
2025-07-13 11:03:50 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:07:17 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:07:17 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:07:17 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:07:17 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:07:17 - startup - INFO - ============================================================
2025-07-13 11:07:17 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:07:17 - startup - INFO - ============================================================
2025-07-13 11:07:17 - startup - INFO - 📅 Startup Time: 2025-07-13T11:07:17.025802
2025-07-13 11:07:17 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:07:17 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:07:17 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:07:17 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:07:17 - startup - INFO - ============================================================
2025-07-13 11:07:17 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:07:17 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-13 11:07:17 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:07:17 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:07:17 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:07:17 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:07:17 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:07:17 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:07:17 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:07:17 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:07:17 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:07:17 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:07:17 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:07:17 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:07:17 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:07:17 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:07:17 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:07:17 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:07:17 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:07:17 - notification_service - INFO - Notification delivery service started
2025-07-13 11:07:17 - allora - INFO - Notification service initialized successfully
2025-07-13 11:07:17 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:10:15 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:10:15 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:10:15 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:10:15 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:10:15 - startup - INFO - ============================================================
2025-07-13 11:10:15 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:10:15 - startup - INFO - ============================================================
2025-07-13 11:10:15 - startup - INFO - 📅 Startup Time: 2025-07-13T11:10:15.497785
2025-07-13 11:10:15 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:10:15 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:10:15 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:10:15 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:10:15 - startup - INFO - ============================================================
2025-07-13 11:10:15 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:10:15 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.005s]
2025-07-13 11:10:15 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:10:15 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:10:15 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:10:16 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:10:16 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:10:16 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:10:16 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:10:16 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:10:16 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:10:16 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:10:16 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:10:16 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:10:16 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:10:16 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:10:16 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:10:16 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:10:16 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:10:16 - notification_service - INFO - Notification delivery service started
2025-07-13 11:10:16 - allora - INFO - Notification service initialized successfully
2025-07-13 11:10:16 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:21:02 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:21:02 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:21:02 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:21:02 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:21:02 - startup - INFO - ============================================================
2025-07-13 11:21:02 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:21:02 - startup - INFO - ============================================================
2025-07-13 11:21:02 - startup - INFO - 📅 Startup Time: 2025-07-13T11:21:02.250008
2025-07-13 11:21:02 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:21:02 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:21:02 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:21:02 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:21:02 - startup - INFO - ============================================================
2025-07-13 11:21:02 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:21:02 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-13 11:21:02 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:21:02 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:21:02 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:21:04 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:21:04 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:21:04 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:21:04 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:21:04 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:21:04 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:21:04 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:21:04 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:21:04 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:21:04 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:21:04 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:21:04 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:21:04 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:21:04 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:21:04 - notification_service - INFO - Notification delivery service started
2025-07-13 11:21:04 - allora - INFO - Notification service initialized successfully
2025-07-13 11:21:04 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:21:40 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:21:40 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:21:40 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:21:40 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:21:40 - startup - INFO - ============================================================
2025-07-13 11:21:40 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:21:40 - startup - INFO - ============================================================
2025-07-13 11:21:40 - startup - INFO - 📅 Startup Time: 2025-07-13T11:21:40.317507
2025-07-13 11:21:40 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:21:40 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:21:40 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:21:40 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:21:40 - startup - INFO - ============================================================
2025-07-13 11:21:40 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:21:40 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-13 11:21:40 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:21:40 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:21:40 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:21:41 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:21:41 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:21:41 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:21:41 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:21:41 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:21:41 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:21:41 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:21:41 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:21:41 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:21:41 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:21:41 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:21:41 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:21:41 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:21:41 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:21:41 - notification_service - INFO - Notification delivery service started
2025-07-13 11:21:41 - allora - INFO - Notification service initialized successfully
2025-07-13 11:21:41 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:28:32 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:28:32 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:28:32 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:28:32 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:28:32 - startup - INFO - ============================================================
2025-07-13 11:28:32 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:28:32 - startup - INFO - ============================================================
2025-07-13 11:28:32 - startup - INFO - 📅 Startup Time: 2025-07-13T11:28:32.825546
2025-07-13 11:28:32 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:28:32 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:28:32 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:28:32 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:28:32 - startup - INFO - ============================================================
2025-07-13 11:28:32 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:28:33 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-13 11:28:33 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:28:33 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:28:33 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:28:33 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:28:33 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:28:33 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:28:33 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:28:33 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:28:33 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:28:33 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:28:33 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:28:33 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:28:33 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:28:33 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:28:33 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:28:33 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:28:33 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:28:33 - notification_service - INFO - Notification delivery service started
2025-07-13 11:28:33 - allora - INFO - Notification service initialized successfully
2025-07-13 11:28:33 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:29:31 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:29:31 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:29:31 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:29:31 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:29:31 - startup - INFO - ============================================================
2025-07-13 11:29:31 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:29:31 - startup - INFO - ============================================================
2025-07-13 11:29:31 - startup - INFO - 📅 Startup Time: 2025-07-13T11:29:31.287888
2025-07-13 11:29:31 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:29:31 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:29:31 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:29:31 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:29:31 - startup - INFO - ============================================================
2025-07-13 11:29:31 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:29:31 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-13 11:29:31 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:29:31 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:29:31 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:29:31 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:29:31 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:29:32 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:29:32 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:29:32 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:29:32 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:29:32 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:29:32 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:29:32 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:29:32 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:29:32 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:29:32 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:29:32 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:29:32 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:29:32 - notification_service - INFO - Notification delivery service started
2025-07-13 11:29:32 - allora - INFO - Notification service initialized successfully
2025-07-13 11:29:32 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:31:34 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:31:34 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:31:34 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:31:34 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:31:34 - startup - INFO - ============================================================
2025-07-13 11:31:34 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:31:34 - startup - INFO - ============================================================
2025-07-13 11:31:34 - startup - INFO - 📅 Startup Time: 2025-07-13T11:31:34.977800
2025-07-13 11:31:34 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:31:34 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:31:34 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:31:34 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:31:34 - startup - INFO - ============================================================
2025-07-13 11:31:34 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:31:35 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-13 11:31:35 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:31:35 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:31:35 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:31:35 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:31:35 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:31:35 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:31:35 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:31:35 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:31:35 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:31:35 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:31:35 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:31:35 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:31:35 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:31:35 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:31:35 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:31:35 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:31:35 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:31:35 - notification_service - INFO - Notification delivery service started
2025-07-13 11:31:35 - allora - INFO - Notification service initialized successfully
2025-07-13 11:31:35 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:35:24 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:35:24 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:35:24 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:35:24 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:35:24 - startup - INFO - ============================================================
2025-07-13 11:35:24 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:35:24 - startup - INFO - ============================================================
2025-07-13 11:35:24 - startup - INFO - 📅 Startup Time: 2025-07-13T11:35:24.529425
2025-07-13 11:35:24 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:35:24 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:35:24 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:35:24 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:35:24 - startup - INFO - ============================================================
2025-07-13 11:35:24 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:35:24 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.011s]
2025-07-13 11:35:24 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:35:24 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:35:24 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:35:25 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:35:25 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:35:25 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:35:25 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:35:25 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:35:25 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:35:25 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:35:25 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:35:25 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:35:25 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:35:25 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:35:25 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:35:25 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:35:25 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:35:25 - notification_service - INFO - Notification delivery service started
2025-07-13 11:35:25 - allora - INFO - Notification service initialized successfully
2025-07-13 11:35:25 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:38:42 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:38:42 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:38:42 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:38:42 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:38:42 - startup - INFO - ============================================================
2025-07-13 11:38:42 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:38:42 - startup - INFO - ============================================================
2025-07-13 11:38:42 - startup - INFO - 📅 Startup Time: 2025-07-13T11:38:42.466442
2025-07-13 11:38:42 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:38:42 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:38:42 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:38:42 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:38:42 - startup - INFO - ============================================================
2025-07-13 11:38:42 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:38:42 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-13 11:38:42 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:38:42 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:38:42 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:38:43 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:38:43 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:38:43 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:38:43 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:38:43 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:38:43 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:38:43 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:38:43 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:38:43 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:38:43 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:38:43 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:38:43 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:38:43 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:38:43 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:38:43 - notification_service - INFO - Notification delivery service started
2025-07-13 11:38:43 - allora - INFO - Notification service initialized successfully
2025-07-13 11:38:43 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:41:55 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:41:55 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:41:55 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:41:55 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:41:55 - startup - INFO - ============================================================
2025-07-13 11:41:55 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:41:55 - startup - INFO - ============================================================
2025-07-13 11:41:55 - startup - INFO - 📅 Startup Time: 2025-07-13T11:41:55.851263
2025-07-13 11:41:55 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:41:55 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:41:55 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:41:55 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:41:55 - startup - INFO - ============================================================
2025-07-13 11:41:55 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:41:56 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-13 11:41:56 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:41:56 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:41:56 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:41:56 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:41:56 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:41:56 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:41:56 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:41:56 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:41:56 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:41:56 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:41:56 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:41:56 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:41:56 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:41:56 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:41:56 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:41:56 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:41:56 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:41:56 - notification_service - INFO - Notification delivery service started
2025-07-13 11:41:56 - allora - INFO - Notification service initialized successfully
2025-07-13 11:41:56 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:44:08 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:44:08 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:44:08 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:44:08 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:44:08 - startup - INFO - ============================================================
2025-07-13 11:44:08 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:44:08 - startup - INFO - ============================================================
2025-07-13 11:44:08 - startup - INFO - 📅 Startup Time: 2025-07-13T11:44:08.487490
2025-07-13 11:44:08 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:44:08 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:44:08 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:44:08 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:44:08 - startup - INFO - ============================================================
2025-07-13 11:44:08 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:44:08 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-13 11:44:08 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:44:08 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:44:08 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:44:09 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:44:09 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:44:09 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:44:09 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:44:09 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:44:09 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:44:09 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:44:09 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:44:09 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:44:09 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:44:09 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:44:09 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:44:09 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:44:09 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:44:09 - notification_service - INFO - Notification delivery service started
2025-07-13 11:44:09 - allora - INFO - Notification service initialized successfully
2025-07-13 11:44:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:44:10 - waitress - INFO - Serving on http://0.0.0.0:5000
2025-07-13 11:46:12 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:46:12 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:46:12 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:46:12 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:46:12 - startup - INFO - ============================================================
2025-07-13 11:46:12 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:46:12 - startup - INFO - ============================================================
2025-07-13 11:46:12 - startup - INFO - 📅 Startup Time: 2025-07-13T11:46:12.754831
2025-07-13 11:46:12 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:46:12 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:46:12 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:46:12 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:46:12 - startup - INFO - ============================================================
2025-07-13 11:46:12 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:46:13 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-13 11:46:13 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:46:13 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:46:13 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:46:13 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:46:13 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:46:13 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:46:13 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:46:13 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:46:13 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:46:13 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:46:13 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:46:13 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:46:13 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:46:13 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:46:13 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:46:13 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:46:13 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:46:13 - notification_service - INFO - Notification delivery service started
2025-07-13 11:46:13 - allora - INFO - Notification service initialized successfully
2025-07-13 11:46:13 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:46:37 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:46:37 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:46:37 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:46:37 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:46:37 - startup - INFO - ============================================================
2025-07-13 11:46:37 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:46:37 - startup - INFO - ============================================================
2025-07-13 11:46:37 - startup - INFO - 📅 Startup Time: 2025-07-13T11:46:37.514582
2025-07-13 11:46:37 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:46:37 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:46:37 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:46:37 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:46:37 - startup - INFO - ============================================================
2025-07-13 11:46:37 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:46:37 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-13 11:46:37 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:46:37 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:46:37 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:46:38 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:46:38 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:46:38 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:46:38 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:46:38 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:46:38 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:46:38 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:46:38 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:46:38 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:46:38 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:46:38 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:46:38 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:46:38 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:46:38 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:46:38 - notification_service - INFO - Notification delivery service started
2025-07-13 11:46:38 - allora - INFO - Notification service initialized successfully
2025-07-13 11:46:38 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:50:31 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:50:31 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:50:31 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:50:31 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:50:31 - startup - INFO - ============================================================
2025-07-13 11:50:31 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:50:31 - startup - INFO - ============================================================
2025-07-13 11:50:31 - startup - INFO - 📅 Startup Time: 2025-07-13T11:50:31.656365
2025-07-13 11:50:31 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:50:31 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:50:31 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:50:31 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:50:31 - startup - INFO - ============================================================
2025-07-13 11:50:31 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:50:31 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-13 11:50:31 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:50:32 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:50:32 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:50:32 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:50:32 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:50:32 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:50:32 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:50:32 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:50:32 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:50:32 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:50:32 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:50:32 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:50:32 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:50:32 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:50:32 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:50:32 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:50:32 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:50:32 - notification_service - INFO - Notification delivery service started
2025-07-13 11:50:32 - allora - INFO - Notification service initialized successfully
2025-07-13 11:50:32 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:51:24 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:51:24 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:51:24 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:51:24 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:51:24 - startup - INFO - ============================================================
2025-07-13 11:51:24 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:51:24 - startup - INFO - ============================================================
2025-07-13 11:51:24 - startup - INFO - 📅 Startup Time: 2025-07-13T11:51:24.927116
2025-07-13 11:51:24 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:51:24 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:51:24 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:51:24 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:51:24 - startup - INFO - ============================================================
2025-07-13 11:51:24 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:51:25 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-13 11:51:25 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:51:25 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:51:25 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:51:25 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:51:25 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:51:25 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:51:25 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:51:25 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:51:25 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:51:25 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:51:25 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:51:25 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:51:25 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:51:25 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:51:25 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:51:25 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:51:25 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:51:25 - notification_service - INFO - Notification delivery service started
2025-07-13 11:51:25 - allora - INFO - Notification service initialized successfully
2025-07-13 11:51:25 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:18:22 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 12:18:22 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 12:18:22 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 12:18:22 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 12:18:22 - startup - INFO - ============================================================
2025-07-13 12:18:22 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 12:18:22 - startup - INFO - ============================================================
2025-07-13 12:18:22 - startup - INFO - 📅 Startup Time: 2025-07-13T12:18:22.390593
2025-07-13 12:18:22 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 12:18:22 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 12:18:22 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 12:18:22 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 12:18:22 - startup - INFO - ============================================================
2025-07-13 12:18:22 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 12:18:22 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 12:18:22 - allora - INFO - 🔧 Pool size: 10
2025-07-13 12:18:22 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 12:18:22 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 12:18:22 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 12:18:23 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.029s]
2025-07-13 12:18:23 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 12:18:23 - allora - INFO - Search API blueprint registered successfully
2025-07-13 12:18:23 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 12:18:24 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 12:18:24 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 12:18:24 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 12:18:24 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 12:18:24 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 12:18:24 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 12:18:24 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 12:18:24 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 12:18:24 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 12:18:24 - allora - INFO - Recommendation system initialized successfully
2025-07-13 12:18:24 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 12:18:24 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 12:18:24 - tracking_system - INFO - Real-time tracking system started
2025-07-13 12:18:24 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 12:18:24 - notification_service - INFO - Notification delivery service started
2025-07-13 12:18:24 - allora - INFO - Notification service initialized successfully
2025-07-13 12:18:24 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:19:59 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 12:19:59 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 12:19:59 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 12:19:59 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 12:19:59 - startup - INFO - ============================================================
2025-07-13 12:19:59 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 12:19:59 - startup - INFO - ============================================================
2025-07-13 12:19:59 - startup - INFO - 📅 Startup Time: 2025-07-13T12:19:59.389738
2025-07-13 12:19:59 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 12:19:59 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 12:19:59 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 12:19:59 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 12:19:59 - startup - INFO - ============================================================
2025-07-13 12:19:59 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 12:19:59 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 12:19:59 - allora - INFO - 🔧 Pool size: 10
2025-07-13 12:19:59 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 12:19:59 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 12:19:59 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 12:19:59 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.028s]
2025-07-13 12:19:59 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 12:19:59 - allora - INFO - Search API blueprint registered successfully
2025-07-13 12:19:59 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 12:20:00 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 12:20:00 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 12:20:00 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 12:20:00 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 12:20:00 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 12:20:00 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 12:20:00 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 12:20:00 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 12:20:00 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 12:20:00 - allora - INFO - Recommendation system initialized successfully
2025-07-13 12:20:00 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 12:20:00 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 12:20:00 - tracking_system - INFO - Real-time tracking system started
2025-07-13 12:20:00 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 12:20:00 - notification_service - INFO - Notification delivery service started
2025-07-13 12:20:00 - allora - INFO - Notification service initialized successfully
2025-07-13 12:20:00 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:22:39 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 12:22:39 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 12:22:39 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 12:22:39 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 12:22:39 - startup - INFO - ============================================================
2025-07-13 12:22:39 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 12:22:39 - startup - INFO - ============================================================
2025-07-13 12:22:39 - startup - INFO - 📅 Startup Time: 2025-07-13T12:22:39.897284
2025-07-13 12:22:39 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 12:22:39 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 12:22:39 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 12:22:39 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 12:22:39 - startup - INFO - ============================================================
2025-07-13 12:22:39 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 12:22:40 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 12:22:40 - allora - INFO - 🔧 Pool size: 10
2025-07-13 12:22:40 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 12:22:40 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 12:22:40 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 12:22:40 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.019s]
2025-07-13 12:22:40 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 12:22:40 - allora - INFO - Search API blueprint registered successfully
2025-07-13 12:22:40 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 12:22:40 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 12:22:40 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 12:22:40 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 12:22:40 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 12:22:40 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 12:22:40 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 12:22:40 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 12:22:40 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 12:22:40 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 12:22:40 - allora - INFO - Recommendation system initialized successfully
2025-07-13 12:22:40 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 12:22:40 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 12:22:40 - tracking_system - INFO - Real-time tracking system started
2025-07-13 12:22:40 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 12:22:40 - notification_service - INFO - Notification delivery service started
2025-07-13 12:22:40 - allora - INFO - Notification service initialized successfully
2025-07-13 12:22:40 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:28:37 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 12:28:37 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 12:28:37 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 12:28:37 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 12:28:37 - startup - INFO - ============================================================
2025-07-13 12:28:37 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 12:28:37 - startup - INFO - ============================================================
2025-07-13 12:28:37 - startup - INFO - 📅 Startup Time: 2025-07-13T12:28:37.411726
2025-07-13 12:28:37 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 12:28:37 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 12:28:37 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 12:28:37 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 12:28:37 - startup - INFO - ============================================================
2025-07-13 12:28:37 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 12:28:37 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 12:28:37 - allora - INFO - 🔧 Pool size: 10
2025-07-13 12:28:37 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 12:28:37 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 12:28:37 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 12:28:37 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.017s]
2025-07-13 12:28:37 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 12:28:37 - allora - INFO - Search API blueprint registered successfully
2025-07-13 12:28:37 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 12:28:38 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 12:28:38 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 12:28:38 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 12:28:38 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 12:28:38 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 12:28:38 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 12:28:38 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 12:28:38 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 12:28:38 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 12:28:38 - allora - INFO - Recommendation system initialized successfully
2025-07-13 12:28:38 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 12:28:38 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 12:28:38 - tracking_system - INFO - Real-time tracking system started
2025-07-13 12:28:38 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 12:28:38 - notification_service - INFO - Notification delivery service started
2025-07-13 12:28:38 - allora - INFO - Notification service initialized successfully
2025-07-13 12:28:38 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:35:34 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 12:35:34 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 12:35:34 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 12:35:34 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 12:35:34 - startup - INFO - ============================================================
2025-07-13 12:35:34 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 12:35:34 - startup - INFO - ============================================================
2025-07-13 12:35:34 - startup - INFO - 📅 Startup Time: 2025-07-13T12:35:34.610997
2025-07-13 12:35:34 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 12:35:34 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 12:35:34 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 12:35:34 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 12:35:34 - startup - INFO - ============================================================
2025-07-13 12:35:34 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 12:35:34 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 12:35:34 - allora - INFO - 🔧 Pool size: 10
2025-07-13 12:35:34 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 12:35:34 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 12:35:34 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 12:35:34 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.028s]
2025-07-13 12:35:34 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 12:35:35 - allora - INFO - Search API blueprint registered successfully
2025-07-13 12:35:35 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 12:35:35 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 12:35:35 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 12:35:35 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 12:35:35 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 12:35:35 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 12:35:35 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 12:35:35 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 12:35:35 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 12:35:35 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 12:35:35 - allora - INFO - Recommendation system initialized successfully
2025-07-13 12:35:35 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 12:35:35 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 12:35:35 - tracking_system - INFO - Real-time tracking system started
2025-07-13 12:35:35 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 12:35:35 - notification_service - INFO - Notification delivery service started
2025-07-13 12:35:35 - allora - INFO - Notification service initialized successfully
2025-07-13 12:35:35 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:40:09 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 12:40:09 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 12:40:09 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 12:40:09 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 12:40:09 - startup - INFO - ============================================================
2025-07-13 12:40:09 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 12:40:09 - startup - INFO - ============================================================
2025-07-13 12:40:09 - startup - INFO - 📅 Startup Time: 2025-07-13T12:40:09.079095
2025-07-13 12:40:09 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 12:40:09 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 12:40:09 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 12:40:09 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 12:40:09 - startup - INFO - ============================================================
2025-07-13 12:40:09 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 12:40:09 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 12:40:09 - allora - INFO - 🔧 Pool size: 10
2025-07-13 12:40:09 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 12:40:09 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 12:40:09 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 12:40:09 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.020s]
2025-07-13 12:40:09 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 12:40:09 - allora - INFO - Search API blueprint registered successfully
2025-07-13 12:40:09 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 12:40:09 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 12:40:09 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 12:40:09 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 12:40:09 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 12:40:09 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 12:40:09 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 12:40:09 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 12:40:09 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 12:40:09 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 12:40:09 - allora - INFO - Recommendation system initialized successfully
2025-07-13 12:40:09 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 12:40:09 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 12:40:09 - tracking_system - INFO - Real-time tracking system started
2025-07-13 12:40:09 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 12:40:09 - notification_service - INFO - Notification delivery service started
2025-07-13 12:40:09 - allora - INFO - Notification service initialized successfully
2025-07-13 12:40:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:44:52 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 12:44:52 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 12:44:52 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 12:44:52 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 12:44:52 - startup - INFO - ============================================================
2025-07-13 12:44:52 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 12:44:52 - startup - INFO - ============================================================
2025-07-13 12:44:52 - startup - INFO - 📅 Startup Time: 2025-07-13T12:44:52.899665
2025-07-13 12:44:52 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 12:44:52 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 12:44:52 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 12:44:52 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 12:44:52 - startup - INFO - ============================================================
2025-07-13 12:44:52 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 12:44:53 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 12:44:53 - allora - INFO - 🔧 Pool size: 10
2025-07-13 12:44:53 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 12:44:53 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 12:44:53 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 12:44:53 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.014s]
2025-07-13 12:44:53 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 12:44:53 - allora - INFO - Search API blueprint registered successfully
2025-07-13 12:44:53 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 12:44:53 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 12:44:53 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 12:44:53 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 12:44:53 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 12:44:53 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 12:44:53 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 12:44:53 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 12:44:53 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 12:44:53 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 12:44:53 - allora - INFO - Recommendation system initialized successfully
2025-07-13 12:44:53 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 12:44:53 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 12:44:53 - tracking_system - INFO - Real-time tracking system started
2025-07-13 12:44:53 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 12:44:53 - notification_service - INFO - Notification delivery service started
2025-07-13 12:44:53 - allora - INFO - Notification service initialized successfully
2025-07-13 12:44:53 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:45:17 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 12:45:17 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 12:45:17 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 12:45:17 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 12:45:17 - startup - INFO - ============================================================
2025-07-13 12:45:17 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 12:45:17 - startup - INFO - ============================================================
2025-07-13 12:45:17 - startup - INFO - 📅 Startup Time: 2025-07-13T12:45:17.711667
2025-07-13 12:45:17 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 12:45:17 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 12:45:17 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 12:45:17 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 12:45:17 - startup - INFO - ============================================================
2025-07-13 12:45:17 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 12:45:17 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 12:45:17 - allora - INFO - 🔧 Pool size: 10
2025-07-13 12:45:17 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 12:45:17 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 12:45:17 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 12:45:18 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.016s]
2025-07-13 12:45:18 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 12:45:18 - allora - INFO - Search API blueprint registered successfully
2025-07-13 12:45:18 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 12:45:18 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 12:45:18 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 12:45:18 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 12:45:18 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 12:45:18 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 12:45:18 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 12:45:18 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 12:45:18 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 12:45:18 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 12:45:18 - allora - INFO - Recommendation system initialized successfully
2025-07-13 12:45:18 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 12:45:18 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 12:45:18 - tracking_system - INFO - Real-time tracking system started
2025-07-13 12:45:18 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 12:45:18 - notification_service - INFO - Notification delivery service started
2025-07-13 12:45:18 - allora - INFO - Notification service initialized successfully
2025-07-13 12:45:18 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:46:08 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 12:46:08 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 12:46:08 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 12:46:08 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 12:46:08 - startup - INFO - ============================================================
2025-07-13 12:46:08 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 12:46:08 - startup - INFO - ============================================================
2025-07-13 12:46:08 - startup - INFO - 📅 Startup Time: 2025-07-13T12:46:08.581694
2025-07-13 12:46:08 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 12:46:08 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 12:46:08 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 12:46:08 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 12:46:08 - startup - INFO - ============================================================
2025-07-13 12:46:08 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 12:46:08 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 12:46:08 - allora - INFO - 🔧 Pool size: 10
2025-07-13 12:46:08 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 12:46:08 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 12:46:08 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 12:46:08 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.021s]
2025-07-13 12:46:08 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 12:46:09 - allora - INFO - Search API blueprint registered successfully
2025-07-13 12:46:09 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 12:46:09 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 12:46:09 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 12:46:09 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 12:46:09 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 12:46:09 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 12:46:09 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 12:46:09 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 12:46:09 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 12:46:09 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 12:46:09 - allora - INFO - Recommendation system initialized successfully
2025-07-13 12:46:09 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 12:46:09 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 12:46:09 - tracking_system - INFO - Real-time tracking system started
2025-07-13 12:46:09 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 12:46:09 - notification_service - INFO - Notification delivery service started
2025-07-13 12:46:09 - allora - INFO - Notification service initialized successfully
2025-07-13 12:46:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:49:52 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 12:49:52 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 12:49:52 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 12:49:52 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 12:49:52 - startup - INFO - ============================================================
2025-07-13 12:49:52 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 12:49:52 - startup - INFO - ============================================================
2025-07-13 12:49:52 - startup - INFO - 📅 Startup Time: 2025-07-13T12:49:52.482442
2025-07-13 12:49:52 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 12:49:52 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 12:49:52 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 12:49:52 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 12:49:52 - startup - INFO - ============================================================
2025-07-13 12:49:52 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 12:49:52 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 12:49:52 - allora - INFO - 🔧 Pool size: 10
2025-07-13 12:49:52 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 12:49:52 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 12:49:52 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 12:49:52 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.021s]
2025-07-13 12:49:52 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 12:49:52 - allora - INFO - Search API blueprint registered successfully
2025-07-13 12:49:52 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 12:49:53 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 12:49:53 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 12:49:53 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 12:49:53 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 12:49:53 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 12:49:53 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 12:49:53 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 12:49:53 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 12:49:53 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 12:49:53 - allora - INFO - Recommendation system initialized successfully
2025-07-13 12:49:53 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 12:49:53 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 12:49:53 - tracking_system - INFO - Real-time tracking system started
2025-07-13 12:49:53 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 12:49:53 - notification_service - INFO - Notification delivery service started
2025-07-13 12:49:53 - allora - INFO - Notification service initialized successfully
2025-07-13 12:49:53 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:55:14 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 12:55:14 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 12:55:14 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 12:55:14 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 12:55:14 - startup - INFO - ============================================================
2025-07-13 12:55:14 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 12:55:14 - startup - INFO - ============================================================
2025-07-13 12:55:14 - startup - INFO - 📅 Startup Time: 2025-07-13T12:55:14.445707
2025-07-13 12:55:14 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 12:55:14 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 12:55:14 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 12:55:14 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 12:55:14 - startup - INFO - ============================================================
2025-07-13 12:55:14 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 12:55:14 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 12:55:14 - allora - INFO - 🔧 Pool size: 10
2025-07-13 12:55:14 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 12:55:14 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 12:55:14 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 12:55:14 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-13 12:55:14 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 12:55:14 - allora - INFO - Search API blueprint registered successfully
2025-07-13 12:55:14 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 12:55:15 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 12:55:15 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 12:55:15 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 12:55:15 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 12:55:15 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 12:55:15 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 12:55:15 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 12:55:15 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 12:55:15 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 12:55:15 - allora - INFO - Recommendation system initialized successfully
2025-07-13 12:55:15 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 12:55:15 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 12:55:15 - tracking_system - INFO - Real-time tracking system started
2025-07-13 12:55:15 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 12:55:15 - notification_service - INFO - Notification delivery service started
2025-07-13 12:55:15 - allora - INFO - Notification service initialized successfully
2025-07-13 12:55:15 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 13:06:39 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 13:06:39 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 13:06:39 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 13:06:39 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 13:06:39 - startup - INFO - ============================================================
2025-07-13 13:06:39 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 13:06:39 - startup - INFO - ============================================================
2025-07-13 13:06:39 - startup - INFO - 📅 Startup Time: 2025-07-13T13:06:39.079353
2025-07-13 13:06:39 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 13:06:39 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 13:06:39 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 13:06:39 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 13:06:39 - startup - INFO - ============================================================
2025-07-13 13:06:39 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 13:06:39 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 13:06:39 - allora - INFO - 🔧 Pool size: 10
2025-07-13 13:06:39 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 13:06:39 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 13:06:39 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 13:06:39 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.019s]
2025-07-13 13:06:39 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 13:06:39 - allora - INFO - Search API blueprint registered successfully
2025-07-13 13:06:39 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 13:06:39 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 13:06:39 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 13:06:39 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 13:06:39 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 13:06:39 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 13:06:39 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 13:06:39 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 13:06:39 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 13:06:39 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 13:06:39 - allora - INFO - Recommendation system initialized successfully
2025-07-13 13:06:39 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 13:06:39 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 13:06:39 - tracking_system - INFO - Real-time tracking system started
2025-07-13 13:06:39 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 13:06:39 - notification_service - INFO - Notification delivery service started
2025-07-13 13:06:39 - allora - INFO - Notification service initialized successfully
2025-07-13 13:06:39 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 14:17:09 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 14:17:09 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 14:17:09 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 14:17:09 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 14:17:09 - startup - INFO - ============================================================
2025-07-13 14:17:09 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 14:17:09 - startup - INFO - ============================================================
2025-07-13 14:17:09 - startup - INFO - 📅 Startup Time: 2025-07-13T14:17:09.344419
2025-07-13 14:17:09 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 14:17:09 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 14:17:09 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 14:17:09 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 14:17:09 - startup - INFO - ============================================================
2025-07-13 14:17:09 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 14:17:09 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 14:17:09 - allora - INFO - 🔧 Pool size: 10
2025-07-13 14:17:09 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 14:17:09 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 14:17:09 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 14:17:09 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.032s]
2025-07-13 14:17:09 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 14:17:09 - allora - INFO - Search API blueprint registered successfully
2025-07-13 14:17:09 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 14:17:10 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 14:17:10 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 14:17:10 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 14:17:10 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 14:17:10 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 14:17:10 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 14:17:10 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 14:17:10 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 14:17:10 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 14:17:10 - allora - INFO - Recommendation system initialized successfully
2025-07-13 14:17:10 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 14:17:10 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 14:17:10 - tracking_system - INFO - Real-time tracking system started
2025-07-13 14:17:10 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 14:17:10 - notification_service - INFO - Notification delivery service started
2025-07-13 14:17:10 - allora - INFO - Notification service initialized successfully
2025-07-13 14:17:10 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 14:17:10 - allora - ERROR - Failed to blacklist token: value is not an integer or out of range
2025-07-13 14:30:55 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 14:30:55 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 14:30:55 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 14:30:55 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 14:30:55 - startup - INFO - ============================================================
2025-07-13 14:30:55 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 14:30:55 - startup - INFO - ============================================================
2025-07-13 14:30:55 - startup - INFO - 📅 Startup Time: 2025-07-13T14:30:55.204882
2025-07-13 14:30:55 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 14:30:55 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 14:30:55 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 14:30:55 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 14:30:55 - startup - INFO - ============================================================
2025-07-13 14:30:55 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 14:30:55 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 14:30:55 - allora - INFO - 🔧 Pool size: 10
2025-07-13 14:30:55 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 14:30:55 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 14:30:55 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 14:30:55 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.023s]
2025-07-13 14:30:55 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 14:30:55 - allora - INFO - Search API blueprint registered successfully
2025-07-13 14:30:55 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 14:30:57 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 14:30:57 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 14:30:57 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 14:30:57 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 14:30:57 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 14:30:57 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 14:30:57 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 14:30:57 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 14:30:57 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 14:30:57 - allora - INFO - Recommendation system initialized successfully
2025-07-13 14:30:57 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 14:30:57 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 14:30:57 - tracking_system - INFO - Real-time tracking system started
2025-07-13 14:30:57 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 14:30:57 - notification_service - INFO - Notification delivery service started
2025-07-13 14:30:57 - allora - INFO - Notification service initialized successfully
2025-07-13 14:30:57 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 14:30:59 - redis_config - WARNING - ⚠️  Redis connection failed: Error 11001 connecting to nonexistent_host:9999. getaddrinfo failed.
2025-07-13 14:31:00 - redis_config - WARNING - ⚠️  Redis connection failed: Error 11001 connecting to nonexistent_host:9999. getaddrinfo failed.
2025-07-13 14:31:02 - allora - WARNING - 404 error for path: /api/cache/test
2025-07-13 14:31:04 - allora - WARNING - 404 error for path: /api/session/test
2025-07-13 14:31:41 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 14:31:41 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 14:31:41 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 14:31:41 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 14:31:41 - startup - INFO - ============================================================
2025-07-13 14:31:41 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 14:31:41 - startup - INFO - ============================================================
2025-07-13 14:31:41 - startup - INFO - 📅 Startup Time: 2025-07-13T14:31:41.344842
2025-07-13 14:31:41 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 14:31:41 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 14:31:41 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 14:31:41 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 14:31:41 - startup - INFO - ============================================================
2025-07-13 14:31:41 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 14:31:41 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 14:31:41 - allora - INFO - 🔧 Pool size: 10
2025-07-13 14:31:41 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 14:31:41 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 14:31:41 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 14:31:41 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 14:31:41 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.016s]
2025-07-13 14:31:41 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 14:31:41 - allora - INFO - Search API blueprint registered successfully
2025-07-13 14:31:41 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 14:31:42 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 14:31:42 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 14:31:42 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 14:31:42 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 14:31:42 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 14:31:42 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 14:31:42 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 14:31:42 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 14:31:42 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 14:31:42 - allora - INFO - Recommendation system initialized successfully
2025-07-13 14:31:42 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 14:31:42 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 14:31:42 - tracking_system - INFO - Real-time tracking system started
2025-07-13 14:31:42 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 14:31:42 - notification_service - INFO - Notification delivery service started
2025-07-13 14:31:42 - allora - INFO - Notification service initialized successfully
2025-07-13 14:31:42 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 14:31:43 - waitress - INFO - Serving on http://127.0.0.1:5000
2025-07-13 14:47:41 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 14:47:41 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 14:47:41 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 14:47:41 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 14:47:41 - startup - INFO - ============================================================
2025-07-13 14:47:41 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 14:47:41 - startup - INFO - ============================================================
2025-07-13 14:47:41 - startup - INFO - 📅 Startup Time: 2025-07-13T14:47:41.298273
2025-07-13 14:47:41 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 14:47:41 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 14:47:41 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 14:47:41 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 14:47:41 - startup - INFO - ============================================================
2025-07-13 14:47:41 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 14:47:41 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 14:47:41 - allora - INFO - 🔧 Pool size: 10
2025-07-13 14:47:41 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 14:47:41 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 14:47:41 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 14:47:41 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 14:47:41 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.031s]
2025-07-13 14:47:41 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 14:47:41 - allora - INFO - Search API blueprint registered successfully
2025-07-13 14:47:41 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 14:47:43 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 14:47:43 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 14:47:43 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 14:47:43 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 14:47:43 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 14:47:43 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 14:47:43 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 14:47:43 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 14:47:43 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 14:47:43 - allora - INFO - Recommendation system initialized successfully
2025-07-13 14:47:43 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 14:47:43 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 14:47:43 - tracking_system - INFO - Real-time tracking system started
2025-07-13 14:47:43 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 14:47:43 - notification_service - INFO - Notification delivery service started
2025-07-13 14:47:43 - allora - INFO - Notification service initialized successfully
2025-07-13 14:47:43 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 14:47:44 - waitress - INFO - Serving on http://127.0.0.1:5000
2025-07-13 14:54:19 - allora - WARNING - 404 error for path: /favicon.ico
2025-07-13 15:12:47 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 15:12:47 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 15:12:47 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 15:12:47 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 15:12:47 - startup - INFO - ============================================================
2025-07-13 15:12:47 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 15:12:47 - startup - INFO - ============================================================
2025-07-13 15:12:47 - startup - INFO - 📅 Startup Time: 2025-07-13T15:12:47.742974
2025-07-13 15:12:47 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 15:12:47 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 15:12:47 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 15:12:47 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 15:12:47 - startup - INFO - ============================================================
2025-07-13 15:12:47 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 15:12:47 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 15:12:47 - allora - INFO - 🔧 Pool size: 10
2025-07-13 15:12:47 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 15:12:47 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 15:12:47 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 15:12:47 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 15:12:48 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.033s]
2025-07-13 15:12:48 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 15:12:48 - allora - INFO - Search API blueprint registered successfully
2025-07-13 15:12:48 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 15:12:48 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 15:12:48 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 15:12:48 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 15:12:48 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 15:12:48 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 15:12:48 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 15:12:48 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 15:12:48 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 15:12:48 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 15:12:48 - allora - INFO - Recommendation system initialized successfully
2025-07-13 15:12:48 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 15:12:48 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 15:12:48 - tracking_system - INFO - Real-time tracking system started
2025-07-13 15:12:48 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 15:12:48 - notification_service - INFO - Notification delivery service started
2025-07-13 15:12:48 - allora - INFO - Notification service initialized successfully
2025-07-13 15:12:48 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 15:21:46 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 15:21:46 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 15:21:46 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 15:21:46 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 15:21:46 - startup - INFO - ============================================================
2025-07-13 15:21:46 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 15:21:46 - startup - INFO - ============================================================
2025-07-13 15:21:46 - startup - INFO - 📅 Startup Time: 2025-07-13T15:21:46.722964
2025-07-13 15:21:46 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 15:21:46 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 15:21:46 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 15:21:46 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 15:21:46 - startup - INFO - ============================================================
2025-07-13 15:21:46 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 15:21:46 - engineio.server - INFO - Server initialized for threading.
2025-07-13 15:21:46 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 15:21:46 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 15:21:46 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 15:21:46 - allora - INFO - 🔧 Pool size: 10
2025-07-13 15:21:46 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 15:21:46 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 15:21:46 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 15:21:46 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 15:21:47 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.019s]
2025-07-13 15:21:47 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 15:21:47 - allora - INFO - Search API blueprint registered successfully
2025-07-13 15:21:47 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 15:21:47 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 15:21:47 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 15:21:47 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 15:21:47 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 15:21:47 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 15:21:47 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 15:21:47 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 15:21:47 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 15:21:47 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 15:21:47 - allora - INFO - Recommendation system initialized successfully
2025-07-13 15:21:47 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 15:21:47 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 15:21:47 - tracking_system - INFO - Real-time tracking system started
2025-07-13 15:21:47 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 15:21:47 - notification_service - INFO - Notification delivery service started
2025-07-13 15:21:47 - allora - INFO - Notification service initialized successfully
2025-07-13 15:21:47 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 15:21:48 - waitress - INFO - Serving on http://127.0.0.1:5000
2025-07-13 15:22:05 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 15:22:05 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 15:22:05 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 15:22:05 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 15:22:05 - startup - INFO - ============================================================
2025-07-13 15:22:05 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 15:22:05 - startup - INFO - ============================================================
2025-07-13 15:22:05 - startup - INFO - 📅 Startup Time: 2025-07-13T15:22:05.561194
2025-07-13 15:22:05 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 15:22:05 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 15:22:05 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 15:22:05 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 15:22:05 - startup - INFO - ============================================================
2025-07-13 15:22:05 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 15:22:05 - engineio.server - INFO - Server initialized for threading.
2025-07-13 15:22:05 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 15:22:05 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 15:22:05 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 15:22:05 - allora - INFO - 🔧 Pool size: 10
2025-07-13 15:22:05 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 15:22:05 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 15:22:05 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 15:22:05 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 15:22:05 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.005s]
2025-07-13 15:22:05 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 15:22:06 - allora - INFO - Search API blueprint registered successfully
2025-07-13 15:22:06 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 15:22:06 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 15:22:06 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 15:22:06 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 15:22:06 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 15:22:06 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 15:22:06 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 15:22:06 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 15:22:06 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 15:22:06 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 15:22:06 - allora - INFO - Recommendation system initialized successfully
2025-07-13 15:22:06 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 15:22:06 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 15:22:06 - tracking_system - INFO - Real-time tracking system started
2025-07-13 15:22:06 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 15:22:06 - notification_service - INFO - Notification delivery service started
2025-07-13 15:22:06 - allora - INFO - Notification service initialized successfully
2025-07-13 15:22:06 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 15:22:36 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 15:22:36 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 15:22:36 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 15:22:36 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 15:22:36 - startup - INFO - ============================================================
2025-07-13 15:22:36 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 15:22:36 - startup - INFO - ============================================================
2025-07-13 15:22:36 - startup - INFO - 📅 Startup Time: 2025-07-13T15:22:36.512308
2025-07-13 15:22:36 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 15:22:36 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 15:22:36 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 15:22:36 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 15:22:36 - startup - INFO - ============================================================
2025-07-13 15:22:36 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 15:22:36 - engineio.server - INFO - Server initialized for threading.
2025-07-13 15:22:36 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 15:22:36 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 15:22:36 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 15:22:36 - allora - INFO - 🔧 Pool size: 10
2025-07-13 15:22:36 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 15:22:36 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 15:22:36 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 15:22:36 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 15:22:36 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.005s]
2025-07-13 15:22:36 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 15:22:37 - allora - INFO - Search API blueprint registered successfully
2025-07-13 15:22:37 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 15:22:37 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 15:22:37 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 15:22:37 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 15:22:37 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 15:22:37 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 15:22:37 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 15:22:37 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 15:22:37 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 15:22:37 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 15:22:37 - allora - INFO - Recommendation system initialized successfully
2025-07-13 15:22:37 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 15:22:37 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 15:22:37 - tracking_system - INFO - Real-time tracking system started
2025-07-13 15:22:37 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 15:22:37 - notification_service - INFO - Notification delivery service started
2025-07-13 15:22:37 - allora - INFO - Notification service initialized successfully
2025-07-13 15:22:37 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 15:22:38 - waitress - INFO - Serving on http://127.0.0.1:5000
2025-07-13 15:23:27 - webhook_handlers - ERROR - Error parsing timestamp 2025-07-13T10:00:00Z: time data '2025-07-13T10:00:00Z' does not match format '%Y-%m-%d %H:%M:%S'
2025-07-13 15:23:28 - webhook_handlers - WARNING - No tracking info found for TEST123456
2025-07-13 15:23:30 - webhook_handlers - WARNING - Invalid Blue Dart webhook signature
2025-07-13 15:23:32 - webhook_handlers - WARNING - Invalid Delhivery webhook signature
2025-07-13 15:23:34 - webhook_handlers - WARNING - Invalid FedEx webhook signature
2025-07-13 15:23:36 - allora - WARNING - 404 error for path: /api/socketio/health
2025-07-13 15:23:38 - allora - WARNING - 404 error for path: /api/socketio/connections
2025-07-13 15:31:39 - webhook_handlers - ERROR - Error parsing timestamp 2025-07-13T10:00:00Z: time data '2025-07-13T10:00:00Z' does not match format '%Y-%m-%d %H:%M:%S'
2025-07-13 15:31:39 - webhook_handlers - WARNING - No tracking info found for TEST123456
2025-07-13 16:13:42 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 16:13:42 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 16:13:42 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 16:13:42 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 16:13:42 - startup - INFO - ============================================================
2025-07-13 16:13:42 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 16:13:42 - startup - INFO - ============================================================
2025-07-13 16:13:42 - startup - INFO - 📅 Startup Time: 2025-07-13T16:13:42.368100
2025-07-13 16:13:42 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 16:13:42 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 16:13:42 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 16:13:42 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 16:13:42 - startup - INFO - ============================================================
2025-07-13 16:13:42 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 16:13:42 - engineio.server - INFO - Server initialized for threading.
2025-07-13 16:13:42 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 16:13:42 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 16:13:42 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 16:13:42 - allora - INFO - 🔧 Pool size: 10
2025-07-13 16:13:42 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 16:13:42 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 16:13:42 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 16:13:42 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 16:13:42 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.025s]
2025-07-13 16:13:42 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 16:13:42 - allora - INFO - Search API blueprint registered successfully
2025-07-13 16:13:42 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 16:13:43 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 16:13:43 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 16:13:43 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 16:13:43 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 16:13:43 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 16:13:43 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 16:13:43 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 16:13:43 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 16:13:43 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 16:13:43 - allora - INFO - Recommendation system initialized successfully
2025-07-13 16:13:43 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 16:13:43 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 16:13:43 - tracking_system - INFO - Real-time tracking system started
2025-07-13 16:13:43 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 16:13:43 - notification_service - INFO - Notification delivery service started
2025-07-13 16:13:43 - allora - INFO - Notification service initialized successfully
2025-07-13 16:13:43 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 16:44:24 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 16:44:24 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 16:44:24 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 16:44:24 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 16:44:24 - startup - INFO - ============================================================
2025-07-13 16:44:24 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 16:44:24 - startup - INFO - ============================================================
2025-07-13 16:44:24 - startup - INFO - 📅 Startup Time: 2025-07-13T16:44:24.048458
2025-07-13 16:44:24 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 16:44:24 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 16:44:24 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 16:44:24 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 16:44:24 - startup - INFO - ============================================================
2025-07-13 16:44:24 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 16:44:24 - engineio.server - INFO - Server initialized for threading.
2025-07-13 16:44:24 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 16:44:24 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 16:44:24 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 16:44:24 - allora - INFO - 🔧 Pool size: 10
2025-07-13 16:44:24 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 16:44:24 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 16:44:24 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 16:44:24 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 16:44:24 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-13 16:44:24 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 16:44:24 - allora - INFO - Search API blueprint registered successfully
2025-07-13 16:44:24 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 16:44:25 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 16:44:25 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 16:44:25 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 16:44:25 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 16:44:25 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 16:44:25 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 16:44:25 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 16:44:25 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 16:44:25 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 16:44:25 - allora - INFO - Recommendation system initialized successfully
2025-07-13 16:44:25 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 16:44:25 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 16:44:25 - tracking_system - INFO - Real-time tracking system started
2025-07-13 16:44:25 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 16:44:25 - notification_service - INFO - Notification delivery service started
2025-07-13 16:44:25 - allora - INFO - Notification service initialized successfully
2025-07-13 16:44:25 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 16:44:26 - waitress - INFO - Serving on http://127.0.0.1:5000
2025-07-13 16:47:45 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 16:47:45 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 16:47:45 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 16:47:45 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 16:47:45 - startup - INFO - ============================================================
2025-07-13 16:47:45 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 16:47:45 - startup - INFO - ============================================================
2025-07-13 16:47:45 - startup - INFO - 📅 Startup Time: 2025-07-13T16:47:45.607280
2025-07-13 16:47:45 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 16:47:45 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 16:47:45 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 16:47:45 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 16:47:45 - startup - INFO - ============================================================
2025-07-13 16:47:45 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 16:47:45 - engineio.server - INFO - Server initialized for threading.
2025-07-13 16:47:45 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 16:47:45 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 16:47:45 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 16:47:45 - allora - INFO - 🔧 Pool size: 10
2025-07-13 16:47:45 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 16:47:45 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 16:47:45 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 16:47:45 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 16:47:45 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.025s]
2025-07-13 16:47:45 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 16:47:46 - allora - INFO - Search API blueprint registered successfully
2025-07-13 16:47:46 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 16:47:46 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 16:47:46 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 16:47:46 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 16:47:46 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 16:47:46 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 16:47:46 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 16:47:46 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 16:47:46 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 16:47:46 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 16:47:46 - allora - INFO - Recommendation system initialized successfully
2025-07-13 16:47:46 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 16:47:46 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 16:47:46 - tracking_system - INFO - Real-time tracking system started
2025-07-13 16:47:46 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 16:47:46 - notification_service - INFO - Notification delivery service started
2025-07-13 16:47:46 - allora - INFO - Notification service initialized successfully
2025-07-13 16:47:46 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 16:47:47 - waitress - INFO - Serving on http://127.0.0.1:5000
2025-07-13 16:52:27 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 16:52:27 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 16:52:27 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 16:52:27 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 16:52:27 - startup - INFO - ============================================================
2025-07-13 16:52:27 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 16:52:27 - startup - INFO - ============================================================
2025-07-13 16:52:27 - startup - INFO - 📅 Startup Time: 2025-07-13T16:52:27.344847
2025-07-13 16:52:27 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 16:52:27 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 16:52:27 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 16:52:27 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 16:52:27 - startup - INFO - ============================================================
2025-07-13 16:52:27 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 16:52:27 - engineio.server - INFO - Server initialized for threading.
2025-07-13 16:52:27 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 16:52:27 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 16:52:27 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 16:52:27 - allora - INFO - 🔧 Pool size: 10
2025-07-13 16:52:27 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 16:52:27 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 16:52:27 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 16:52:27 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 16:52:27 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.004s]
2025-07-13 16:52:27 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 16:52:27 - allora - INFO - Search API blueprint registered successfully
2025-07-13 16:52:27 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 16:52:28 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 16:52:28 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 16:52:28 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 16:52:28 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 16:52:28 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 16:52:28 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 16:52:28 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 16:52:28 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 16:52:28 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 16:52:28 - allora - INFO - Recommendation system initialized successfully
2025-07-13 16:52:28 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 16:52:28 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 16:52:28 - tracking_system - INFO - Real-time tracking system started
2025-07-13 16:52:28 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 16:52:28 - notification_service - INFO - Notification delivery service started
2025-07-13 16:52:28 - allora - INFO - Notification service initialized successfully
2025-07-13 16:52:28 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 16:52:29 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-07-13 16:52:29 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-13 16:53:12 - allora - WARNING - 404 error for path: /.well-known/appspecific/com.chrome.devtools.json
2025-07-13 16:54:52 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 16:54:52 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 16:54:52 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 16:54:52 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 16:54:52 - startup - INFO - ============================================================
2025-07-13 16:54:52 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 16:54:52 - startup - INFO - ============================================================
2025-07-13 16:54:52 - startup - INFO - 📅 Startup Time: 2025-07-13T16:54:52.947833
2025-07-13 16:54:52 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 16:54:52 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 16:54:52 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 16:54:52 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 16:54:52 - startup - INFO - ============================================================
2025-07-13 16:54:52 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 16:54:53 - engineio.server - INFO - Server initialized for threading.
2025-07-13 16:54:53 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 16:54:53 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 16:54:53 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 16:54:53 - allora - INFO - 🔧 Pool size: 10
2025-07-13 16:54:53 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 16:54:53 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 16:54:53 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 16:54:53 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 16:54:53 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.020s]
2025-07-13 16:54:53 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 16:54:53 - allora - INFO - Search API blueprint registered successfully
2025-07-13 16:54:53 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 16:54:53 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 16:54:53 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 16:54:53 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 16:54:53 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 16:54:53 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 16:54:53 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 16:54:53 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 16:54:53 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 16:54:53 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 16:54:53 - allora - INFO - Recommendation system initialized successfully
2025-07-13 16:54:53 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 16:54:53 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 16:54:53 - tracking_system - INFO - Real-time tracking system started
2025-07-13 16:54:53 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 16:54:53 - notification_service - INFO - Notification delivery service started
2025-07-13 16:54:53 - allora - INFO - Notification service initialized successfully
2025-07-13 16:54:53 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 16:54:55 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-07-13 16:54:55 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-13 16:55:34 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 16:55:34] "GET /socketio-test HTTP/1.1" 200 -
2025-07-13 16:57:50 - engineio.server - INFO - EmqWmhS2IiRmAf2GAAAA: Sending packet OPEN data {'sid': 'EmqWmhS2IiRmAf2GAAAA', 'upgrades': ['websocket'], 'pingTimeout': 60000, 'pingInterval': 25000, 'maxPayload': 1000000}
2025-07-13 16:57:50 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 16:57:50] "GET /socket.io/?transport=polling&EIO=4&t=1752406068.642394 HTTP/1.1" 200 -
2025-07-13 16:57:52 - engineio.server - INFO - EmqWmhS2IiRmAf2GAAAA: Received request to upgrade to websocket
2025-07-13 16:57:52 - engineio.server - INFO - EmqWmhS2IiRmAf2GAAAA: Upgrade to websocket successful
2025-07-13 16:57:52 - engineio.server - INFO - EmqWmhS2IiRmAf2GAAAA: Received packet MESSAGE data 0{}
2025-07-13 16:57:52 - socketio.server - INFO - LDhRSXpzPQ3iY7gNAAAB is entering room guests [/]
2025-07-13 16:57:52 - flask_socketio_manager - INFO - Guest LDhRSXpzPQ3iY7gNAAAB connected via SocketIO
2025-07-13 16:57:52 - socketio.server - INFO - emitting event "connection_established" to LDhRSXpzPQ3iY7gNAAAB [/]
2025-07-13 16:57:52 - engineio.server - INFO - EmqWmhS2IiRmAf2GAAAA: Sending packet MESSAGE data 2["connection_established",{"status":"connected","timestamp":"2025-07-13T16:57:52.770960","user_id":null,"session_id":"LDhRSXpzPQ3iY7gNAAAB"}]
2025-07-13 16:57:52 - engineio.server - INFO - EmqWmhS2IiRmAf2GAAAA: Sending packet MESSAGE data 0{"sid":"LDhRSXpzPQ3iY7gNAAAB"}
2025-07-13 16:57:52 - engineio.server - INFO - EmqWmhS2IiRmAf2GAAAA: Received packet MESSAGE data 2["ping"]
2025-07-13 16:57:52 - socketio.server - INFO - received event "ping" from LDhRSXpzPQ3iY7gNAAAB [/]
2025-07-13 16:57:52 - socketio.server - INFO - emitting event "pong" to LDhRSXpzPQ3iY7gNAAAB [/]
2025-07-13 16:57:52 - engineio.server - INFO - EmqWmhS2IiRmAf2GAAAA: Sending packet MESSAGE data 2["pong",{"timestamp":"2025-07-13T16:57:52.777946"}]
2025-07-13 16:57:54 - engineio.server - INFO - EmqWmhS2IiRmAf2GAAAA: Received packet MESSAGE data 2["subscribe",{"events":["inventory","prices","orders"]}]
2025-07-13 16:57:54 - socketio.server - INFO - received event "subscribe" from LDhRSXpzPQ3iY7gNAAAB [/]
2025-07-13 16:57:54 - socketio.server - INFO - LDhRSXpzPQ3iY7gNAAAB is entering room events_inventory [/]
2025-07-13 16:57:54 - socketio.server - INFO - LDhRSXpzPQ3iY7gNAAAB is entering room events_prices [/]
2025-07-13 16:57:54 - socketio.server - INFO - LDhRSXpzPQ3iY7gNAAAB is entering room events_orders [/]
2025-07-13 16:57:54 - socketio.server - INFO - emitting event "subscribed" to LDhRSXpzPQ3iY7gNAAAB [/]
2025-07-13 16:57:54 - engineio.server - INFO - EmqWmhS2IiRmAf2GAAAA: Sending packet MESSAGE data 2["subscribed",{"events":["inventory","prices","orders"],"timestamp":"2025-07-13T16:57:54.792212"}]
2025-07-13 16:57:56 - engineio.server - INFO - EmqWmhS2IiRmAf2GAAAA: Received packet MESSAGE data 2["heartbeat"]
2025-07-13 16:57:56 - socketio.server - INFO - received event "heartbeat" from LDhRSXpzPQ3iY7gNAAAB [/]
2025-07-13 16:57:56 - socketio.server - INFO - emitting event "heartbeat_ack" to LDhRSXpzPQ3iY7gNAAAB [/]
2025-07-13 16:57:56 - engineio.server - INFO - EmqWmhS2IiRmAf2GAAAA: Sending packet MESSAGE data 2["heartbeat_ack",{"timestamp":"2025-07-13T16:57:56.799359"}]
2025-07-13 16:57:58 - engineio.server - INFO - EmqWmhS2IiRmAf2GAAAA: Received packet MESSAGE data 1
2025-07-13 16:57:58 - socketio.server - INFO - LDhRSXpzPQ3iY7gNAAAB is leaving room guests [/]
2025-07-13 16:57:58 - engineio.server - INFO - EmqWmhS2IiRmAf2GAAAA: Received packet CLOSE data 
2025-07-13 16:57:58 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 16:57:58] "GET /socket.io/?transport=websocket&EIO=4&sid=EmqWmhS2IiRmAf2GAAAA&t=1752406070.7011864 HTTP/1.1" 200 -
2025-07-13 16:58:01 - engineio.server - INFO - EyqSnvnHGFoxEY_tAAAC: Sending packet OPEN data {'sid': 'EyqSnvnHGFoxEY_tAAAC', 'upgrades': ['websocket'], 'pingTimeout': 60000, 'pingInterval': 25000, 'maxPayload': 1000000}
2025-07-13 16:58:01 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 16:58:01] "GET /socket.io/?transport=polling&EIO=4&t=1752406079.824653 HTTP/1.1" 200 -
2025-07-13 16:58:03 - engineio.server - INFO - EyqSnvnHGFoxEY_tAAAC: Received request to upgrade to websocket
2025-07-13 16:58:03 - engineio.server - INFO - EyqSnvnHGFoxEY_tAAAC: Upgrade to websocket successful
2025-07-13 16:58:14 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 16:58:14] "GET /socket.io/?transport=websocket&EIO=4&sid=EyqSnvnHGFoxEY_tAAAC&t=1752406081.916322 HTTP/1.1" 200 -
2025-07-13 16:58:16 - engineio.server - INFO - 4NivOopDYSZvoE9vAAAD: Sending packet OPEN data {'sid': '4NivOopDYSZvoE9vAAAD', 'upgrades': ['websocket'], 'pingTimeout': 60000, 'pingInterval': 25000, 'maxPayload': 1000000}
2025-07-13 16:58:16 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 16:58:16] "GET /socket.io/?transport=polling&EIO=4&t=1752406094.0060182 HTTP/1.1" 200 -
2025-07-13 16:58:18 - engineio.server - INFO - 4NivOopDYSZvoE9vAAAD: Received request to upgrade to websocket
2025-07-13 16:58:18 - engineio.server - INFO - 4NivOopDYSZvoE9vAAAD: Upgrade to websocket successful
2025-07-13 16:58:18 - engineio.server - INFO - 4NivOopDYSZvoE9vAAAD: Received packet MESSAGE data 0{"user_id":"test_user_123","is_admin":false}
2025-07-13 16:58:18 - socketio.server - INFO - cbK0sAcvImxGawi0AAAE is entering room user_test_user_123 [/]
2025-07-13 16:58:18 - flask_socketio_manager - INFO - User test_user_123 connected via SocketIO
2025-07-13 16:58:18 - socketio.server - INFO - emitting event "connection_established" to cbK0sAcvImxGawi0AAAE [/]
2025-07-13 16:58:18 - engineio.server - INFO - 4NivOopDYSZvoE9vAAAD: Sending packet MESSAGE data 2["connection_established",{"status":"connected","timestamp":"2025-07-13T16:58:18.202764","user_id":"test_user_123","session_id":"cbK0sAcvImxGawi0AAAE"}]
2025-07-13 16:58:18 - engineio.server - INFO - 4NivOopDYSZvoE9vAAAD: Sending packet MESSAGE data 0{"sid":"cbK0sAcvImxGawi0AAAE"}
2025-07-13 16:58:18 - engineio.server - INFO - 4NivOopDYSZvoE9vAAAD: Received packet CLOSE data 
2025-07-13 16:58:18 - socketio.server - INFO - cbK0sAcvImxGawi0AAAE is leaving room user_test_user_123 [/]
2025-07-13 16:58:18 - flask_socketio_manager - INFO - User test_user_123 disconnected from SocketIO
2025-07-13 16:58:18 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 16:58:18] "GET /socket.io/?transport=websocket&EIO=4&sid=4NivOopDYSZvoE9vAAAD&t=1752406096.1410165 HTTP/1.1" 200 -
2025-07-13 17:00:03 - engineio.server - INFO - JGI9U8nTXEBuRFn-AAAF: Sending packet OPEN data {'sid': 'JGI9U8nTXEBuRFn-AAAF', 'upgrades': ['websocket'], 'pingTimeout': 60000, 'pingInterval': 25000, 'maxPayload': 1000000}
2025-07-13 17:00:03 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 17:00:03] "GET /socket.io/?transport=polling&EIO=4&t=1752406201.6278756 HTTP/1.1" 200 -
2025-07-13 17:00:05 - engineio.server - INFO - JGI9U8nTXEBuRFn-AAAF: Received request to upgrade to websocket
2025-07-13 17:00:05 - engineio.server - INFO - JGI9U8nTXEBuRFn-AAAF: Upgrade to websocket successful
2025-07-13 17:00:05 - engineio.server - INFO - JGI9U8nTXEBuRFn-AAAF: Received packet MESSAGE data 0{}
2025-07-13 17:00:05 - socketio.server - INFO - _wYl7Lyfd-_qzwulAAAG is entering room guests [/]
2025-07-13 17:00:05 - flask_socketio_manager - INFO - Guest _wYl7Lyfd-_qzwulAAAG connected via SocketIO
2025-07-13 17:00:05 - socketio.server - INFO - emitting event "connection_established" to _wYl7Lyfd-_qzwulAAAG [/]
2025-07-13 17:00:05 - engineio.server - INFO - JGI9U8nTXEBuRFn-AAAF: Sending packet MESSAGE data 2["connection_established",{"status":"connected","timestamp":"2025-07-13T17:00:05.803663","user_id":null,"session_id":"_wYl7Lyfd-_qzwulAAAG"}]
2025-07-13 17:00:05 - engineio.server - INFO - JGI9U8nTXEBuRFn-AAAF: Sending packet MESSAGE data 0{"sid":"_wYl7Lyfd-_qzwulAAAG"}
2025-07-13 17:00:16 - engineio.server - INFO - JGI9U8nTXEBuRFn-AAAF: Received packet MESSAGE data 1
2025-07-13 17:00:16 - socketio.server - INFO - _wYl7Lyfd-_qzwulAAAG is leaving room guests [/]
2025-07-13 17:00:16 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 17:00:16] "GET /socket.io/?transport=websocket&EIO=4&sid=JGI9U8nTXEBuRFn-AAAF&t=**********.7156057 HTTP/1.1" 200 -
2025-07-13 17:02:34 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 17:02:34] "GET /api/health HTTP/1.1" 200 -
2025-07-13 17:02:36 - engineio.server - ERROR - The client is using an unsupported version of the Socket.IO or Engine.IO protocols (further occurrences of this error will be logged with level INFO)
2025-07-13 17:02:36 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 17:02:36] "[31m[1mGET /socket.io/ HTTP/1.1[0m" 400 -
2025-07-13 17:02:39 - engineio.server - INFO - 6X0YucKUy64lGVohAAAH: Sending packet OPEN data {'sid': '6X0YucKUy64lGVohAAAH', 'upgrades': ['websocket'], 'pingTimeout': 60000, 'pingInterval': 25000, 'maxPayload': 1000000}
2025-07-13 17:02:39 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 17:02:39] "GET /socket.io/?transport=polling&EIO=4&t=**********.0064561 HTTP/1.1" 200 -
2025-07-13 17:02:41 - engineio.server - INFO - 6X0YucKUy64lGVohAAAH: Received request to upgrade to websocket
2025-07-13 17:02:41 - engineio.server - INFO - 6X0YucKUy64lGVohAAAH: Upgrade to websocket successful
2025-07-13 17:02:41 - engineio.server - INFO - 6X0YucKUy64lGVohAAAH: Received packet MESSAGE data 0{}
2025-07-13 17:02:41 - socketio.server - INFO - qti7I4673Q14vQffAAAI is entering room guests [/]
2025-07-13 17:02:41 - flask_socketio_manager - INFO - Guest qti7I4673Q14vQffAAAI connected via SocketIO
2025-07-13 17:02:41 - socketio.server - INFO - emitting event "connection_established" to qti7I4673Q14vQffAAAI [/]
2025-07-13 17:02:41 - engineio.server - INFO - 6X0YucKUy64lGVohAAAH: Sending packet MESSAGE data 2["connection_established",{"status":"connected","timestamp":"2025-07-13T17:02:41.137749","user_id":null,"session_id":"qti7I4673Q14vQffAAAI"}]
2025-07-13 17:02:41 - engineio.server - INFO - 6X0YucKUy64lGVohAAAH: Sending packet MESSAGE data 0{"sid":"qti7I4673Q14vQffAAAI"}
2025-07-13 17:02:41 - engineio.server - INFO - 6X0YucKUy64lGVohAAAH: Received packet MESSAGE data 2["ping"]
2025-07-13 17:02:41 - socketio.server - INFO - received event "ping" from qti7I4673Q14vQffAAAI [/]
2025-07-13 17:02:41 - socketio.server - INFO - emitting event "pong" to qti7I4673Q14vQffAAAI [/]
2025-07-13 17:02:41 - engineio.server - INFO - 6X0YucKUy64lGVohAAAH: Sending packet MESSAGE data 2["pong",{"timestamp":"2025-07-13T17:02:41.146727"}]
2025-07-13 17:02:42 - engineio.server - INFO - 6X0YucKUy64lGVohAAAH: Received packet MESSAGE data 2["subscribe",{"events":["inventory","prices"]}]
2025-07-13 17:02:42 - socketio.server - INFO - received event "subscribe" from qti7I4673Q14vQffAAAI [/]
2025-07-13 17:02:42 - socketio.server - INFO - qti7I4673Q14vQffAAAI is entering room events_inventory [/]
2025-07-13 17:02:42 - socketio.server - INFO - qti7I4673Q14vQffAAAI is entering room events_prices [/]
2025-07-13 17:02:42 - socketio.server - INFO - emitting event "subscribed" to qti7I4673Q14vQffAAAI [/]
2025-07-13 17:02:42 - engineio.server - INFO - 6X0YucKUy64lGVohAAAH: Sending packet MESSAGE data 2["subscribed",{"events":["inventory","prices"],"timestamp":"2025-07-13T17:02:42.151633"}]
2025-07-13 17:02:45 - engineio.server - INFO - 6X0YucKUy64lGVohAAAH: Received packet MESSAGE data 1
2025-07-13 17:02:45 - socketio.server - INFO - qti7I4673Q14vQffAAAI is leaving room guests [/]
2025-07-13 17:02:45 - engineio.server - INFO - 6X0YucKUy64lGVohAAAH: Received packet CLOSE data 
2025-07-13 17:02:45 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 17:02:45] "GET /socket.io/?transport=websocket&EIO=4&sid=6X0YucKUy64lGVohAAAH&t=1752406359.0741348 HTTP/1.1" 200 -
2025-07-13 17:04:56 - engineio.server - INFO - tN1oMiD3iFKxlzPaAAAJ: Sending packet OPEN data {'sid': 'tN1oMiD3iFKxlzPaAAAJ', 'upgrades': ['websocket'], 'pingTimeout': 60000, 'pingInterval': 25000, 'maxPayload': 1000000}
2025-07-13 17:04:56 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 17:04:56] "GET /socket.io/?transport=polling&EIO=4&t=1752406494.739284 HTTP/1.1" 200 -
2025-07-13 17:04:58 - engineio.server - INFO - tN1oMiD3iFKxlzPaAAAJ: Received request to upgrade to websocket
2025-07-13 17:04:58 - engineio.server - INFO - tN1oMiD3iFKxlzPaAAAJ: Upgrade to websocket successful
2025-07-13 17:04:58 - engineio.server - INFO - tN1oMiD3iFKxlzPaAAAJ: Received packet MESSAGE data 0{}
2025-07-13 17:04:58 - socketio.server - INFO - LO1ffa3Wg7FHf2PFAAAK is entering room guests [/]
2025-07-13 17:04:58 - flask_socketio_manager - INFO - Guest LO1ffa3Wg7FHf2PFAAAK connected via SocketIO
2025-07-13 17:04:58 - socketio.server - INFO - emitting event "connection_established" to LO1ffa3Wg7FHf2PFAAAK [/]
2025-07-13 17:04:58 - engineio.server - INFO - tN1oMiD3iFKxlzPaAAAJ: Sending packet MESSAGE data 2["connection_established",{"status":"connected","timestamp":"2025-07-13T17:04:58.848747","user_id":null,"session_id":"LO1ffa3Wg7FHf2PFAAAK"}]
2025-07-13 17:04:58 - engineio.server - INFO - tN1oMiD3iFKxlzPaAAAJ: Sending packet MESSAGE data 0{"sid":"LO1ffa3Wg7FHf2PFAAAK"}
2025-07-13 17:05:09 - engineio.server - INFO - tN1oMiD3iFKxlzPaAAAJ: Received packet MESSAGE data 1
2025-07-13 17:05:09 - socketio.server - INFO - LO1ffa3Wg7FHf2PFAAAK is leaving room guests [/]
2025-07-13 17:05:09 - engineio.server - INFO - tN1oMiD3iFKxlzPaAAAJ: Received packet CLOSE data 
2025-07-13 17:05:09 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 17:05:09] "GET /socket.io/?transport=websocket&EIO=4&sid=tN1oMiD3iFKxlzPaAAAJ&t=1752406496.7923627 HTTP/1.1" 200 -
2025-07-13 17:07:54 - engineio.server - INFO - 16qFSMeyFMriSgwCAAAL: Sending packet OPEN data {'sid': '16qFSMeyFMriSgwCAAAL', 'upgrades': ['websocket'], 'pingTimeout': 60000, 'pingInterval': 25000, 'maxPayload': 1000000}
2025-07-13 17:07:54 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 17:07:54] "GET /socket.io/?transport=polling&EIO=4&t=1752406672.0073967 HTTP/1.1" 200 -
2025-07-13 17:07:56 - engineio.server - INFO - 16qFSMeyFMriSgwCAAAL: Received request to upgrade to websocket
2025-07-13 17:07:56 - engineio.server - INFO - 16qFSMeyFMriSgwCAAAL: Upgrade to websocket successful
2025-07-13 17:07:56 - engineio.server - INFO - 16qFSMeyFMriSgwCAAAL: Received packet MESSAGE data 0{}
2025-07-13 17:07:56 - socketio.server - INFO - NvB2rtq2DGYTNRFnAAAM is entering room guests [/]
2025-07-13 17:07:56 - flask_socketio_manager - INFO - Guest NvB2rtq2DGYTNRFnAAAM connected via SocketIO
2025-07-13 17:07:56 - socketio.server - INFO - emitting event "connection_established" to NvB2rtq2DGYTNRFnAAAM [/]
2025-07-13 17:07:56 - engineio.server - INFO - 16qFSMeyFMriSgwCAAAL: Sending packet MESSAGE data 2["connection_established",{"status":"connected","timestamp":"2025-07-13T17:07:56.111748","user_id":null,"session_id":"NvB2rtq2DGYTNRFnAAAM"}]
2025-07-13 17:07:56 - engineio.server - INFO - 16qFSMeyFMriSgwCAAAL: Sending packet MESSAGE data 0{"sid":"NvB2rtq2DGYTNRFnAAAM"}
2025-07-13 17:08:04 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 17:08:04 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 17:08:04 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 17:08:04 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 17:08:04 - startup - INFO - ============================================================
2025-07-13 17:08:04 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 17:08:04 - startup - INFO - ============================================================
2025-07-13 17:08:04 - startup - INFO - 📅 Startup Time: 2025-07-13T17:08:04.479696
2025-07-13 17:08:04 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 17:08:04 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 17:08:04 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 17:08:04 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 17:08:04 - startup - INFO - ============================================================
2025-07-13 17:08:04 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 17:08:04 - engineio.server - INFO - Server initialized for threading.
2025-07-13 17:08:04 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 17:08:04 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 17:08:04 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 17:08:04 - allora - INFO - 🔧 Pool size: 10
2025-07-13 17:08:04 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 17:08:04 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 17:08:04 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 17:08:04 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 17:08:04 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.016s]
2025-07-13 17:08:04 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 17:08:05 - allora - INFO - Search API blueprint registered successfully
2025-07-13 17:08:05 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 17:08:05 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 17:08:05 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 17:08:05 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 17:08:05 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 17:08:05 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 17:08:05 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 17:08:05 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 17:08:05 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 17:08:05 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 17:08:05 - allora - INFO - Recommendation system initialized successfully
2025-07-13 17:08:05 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 17:08:05 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 17:08:05 - tracking_system - INFO - Real-time tracking system started
2025-07-13 17:08:05 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 17:08:05 - notification_service - INFO - Notification delivery service started
2025-07-13 17:08:05 - allora - INFO - Notification service initialized successfully
2025-07-13 17:08:05 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 17:08:06 - socketio.server - INFO - emitting event "inventory_update" to all [/]
2025-07-13 17:08:06 - flask_socketio_manager - INFO - Broadcasted inventory update for product 123
2025-07-13 17:08:08 - socketio.server - INFO - emitting event "price_update" to all [/]
2025-07-13 17:08:08 - flask_socketio_manager - INFO - Broadcasted price update for product 456
2025-07-13 17:08:10 - socketio.server - INFO - emitting event "cart_update" to user_test_user_123 [/]
2025-07-13 17:08:10 - flask_socketio_manager - INFO - Sent cart update to user test_user_123
2025-07-13 17:08:12 - socketio.server - INFO - emitting event "notification" to user_test_user_123 [/]
2025-07-13 17:08:12 - flask_socketio_manager - INFO - Sent notification to user test_user_123
2025-07-13 17:08:14 - socketio.server - INFO - emitting event "admin_notification" to admin [/]
2025-07-13 17:08:14 - flask_socketio_manager - INFO - Broadcasted message to admin users
2025-07-13 17:08:16 - engineio.server - INFO - 16qFSMeyFMriSgwCAAAL: Received packet MESSAGE data 1
2025-07-13 17:08:16 - socketio.server - INFO - NvB2rtq2DGYTNRFnAAAM is leaving room guests [/]
2025-07-13 17:08:16 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 17:08:16] "GET /socket.io/?transport=websocket&EIO=4&sid=16qFSMeyFMriSgwCAAAL&t=1752406674.054071 HTTP/1.1" 200 -
2025-07-13 17:10:13 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 17:10:13 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 17:10:13 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 17:10:13 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 17:10:13 - startup - INFO - ============================================================
2025-07-13 17:10:13 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 17:10:13 - startup - INFO - ============================================================
2025-07-13 17:10:13 - startup - INFO - 📅 Startup Time: 2025-07-13T17:10:13.019174
2025-07-13 17:10:13 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 17:10:13 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 17:10:13 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 17:10:13 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 17:10:13 - startup - INFO - ============================================================
2025-07-13 17:10:13 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 17:10:13 - engineio.server - INFO - Server initialized for threading.
2025-07-13 17:10:13 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 17:10:13 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 17:10:13 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 17:10:13 - allora - INFO - 🔧 Pool size: 10
2025-07-13 17:10:13 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 17:10:13 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 17:10:13 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 17:10:13 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 17:10:13 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.011s]
2025-07-13 17:10:13 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 17:10:13 - allora - INFO - Search API blueprint registered successfully
2025-07-13 17:10:13 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 17:10:14 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 17:10:14 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 17:10:14 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 17:10:14 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 17:10:14 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 17:10:14 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 17:10:14 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 17:10:14 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 17:10:14 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 17:10:14 - allora - INFO - Recommendation system initialized successfully
2025-07-13 17:10:14 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 17:10:14 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 17:10:14 - tracking_system - INFO - Real-time tracking system started
2025-07-13 17:10:14 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 17:10:14 - notification_service - INFO - Notification delivery service started
2025-07-13 17:10:14 - allora - INFO - Notification service initialized successfully
2025-07-13 17:10:14 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 17:13:06 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 17:13:06 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 17:13:06 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 17:13:06 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 17:13:06 - startup - INFO - ============================================================
2025-07-13 17:13:06 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 17:13:06 - startup - INFO - ============================================================
2025-07-13 17:13:06 - startup - INFO - 📅 Startup Time: 2025-07-13T17:13:06.361267
2025-07-13 17:13:06 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 17:13:06 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 17:13:06 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 17:13:06 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 17:13:06 - startup - INFO - ============================================================
2025-07-13 17:13:06 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 17:13:06 - engineio.server - INFO - Server initialized for threading.
2025-07-13 17:13:06 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 17:13:06 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 17:13:06 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 17:13:06 - allora - INFO - 🔧 Pool size: 10
2025-07-13 17:13:06 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 17:13:06 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 17:13:06 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 17:13:06 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 17:13:06 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.009s]
2025-07-13 17:13:06 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 17:13:06 - allora - INFO - Search API blueprint registered successfully
2025-07-13 17:13:06 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 17:13:07 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 17:13:07 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 17:13:07 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 17:13:07 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 17:13:07 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 17:13:07 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 17:13:07 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 17:13:07 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 17:13:07 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 17:13:07 - allora - INFO - Recommendation system initialized successfully
2025-07-13 17:13:07 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 17:13:07 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 17:13:07 - tracking_system - INFO - Real-time tracking system started
2025-07-13 17:13:07 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 17:13:07 - notification_service - INFO - Notification delivery service started
2025-07-13 17:13:07 - allora - INFO - Notification service initialized successfully
2025-07-13 17:13:07 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 17:13:09 - engineio.server - INFO - EWtF965mjS4JrkruAAAN: Sending packet OPEN data {'sid': 'EWtF965mjS4JrkruAAAN', 'upgrades': ['websocket'], 'pingTimeout': 60000, 'pingInterval': 25000, 'maxPayload': 1000000}
2025-07-13 17:13:09 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 17:13:09] "GET /socket.io/?transport=polling&EIO=4&t=1752406987.8722355 HTTP/1.1" 200 -
2025-07-13 17:13:12 - engineio.server - INFO - EWtF965mjS4JrkruAAAN: Received request to upgrade to websocket
2025-07-13 17:13:12 - engineio.server - INFO - EWtF965mjS4JrkruAAAN: Upgrade to websocket successful
2025-07-13 17:13:12 - engineio.server - INFO - EWtF965mjS4JrkruAAAN: Received packet MESSAGE data 0{}
2025-07-13 17:13:12 - socketio.server - INFO - jEPDqsVNd_rI_CDsAAAO is entering room guests [/]
2025-07-13 17:13:12 - flask_socketio_manager - INFO - Guest jEPDqsVNd_rI_CDsAAAO connected via SocketIO
2025-07-13 17:13:12 - socketio.server - INFO - emitting event "connection_established" to jEPDqsVNd_rI_CDsAAAO [/]
2025-07-13 17:13:12 - engineio.server - INFO - EWtF965mjS4JrkruAAAN: Sending packet MESSAGE data 2["connection_established",{"status":"connected","timestamp":"2025-07-13T17:13:12.033328","user_id":null,"session_id":"jEPDqsVNd_rI_CDsAAAO"}]
2025-07-13 17:13:12 - engineio.server - INFO - EWtF965mjS4JrkruAAAN: Sending packet MESSAGE data 0{"sid":"jEPDqsVNd_rI_CDsAAAO"}
2025-07-13 17:13:14 - socketio.server - INFO - emitting event "inventory_update" to all [/]
2025-07-13 17:13:16 - socketio.server - INFO - emitting event "inventory_update" to all [/]
2025-07-13 17:13:16 - flask_socketio_manager - INFO - Broadcasted inventory update for product 888
2025-07-13 17:13:18 - socketio.server - INFO - emitting event "price_update" to all [/]
2025-07-13 17:13:20 - engineio.server - INFO - EWtF965mjS4JrkruAAAN: Received packet MESSAGE data 1
2025-07-13 17:13:20 - socketio.server - INFO - jEPDqsVNd_rI_CDsAAAO is leaving room guests [/]
2025-07-13 17:13:20 - engineio.server - INFO - EWtF965mjS4JrkruAAAN: Received packet CLOSE data 
2025-07-13 17:13:20 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 17:13:20] "GET /socket.io/?transport=websocket&EIO=4&sid=EWtF965mjS4JrkruAAAN&t=1752406989.9428968 HTTP/1.1" 200 -
2025-07-13 17:16:57 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 17:16:57 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 17:16:57 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 17:16:57 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 17:16:57 - startup - INFO - ============================================================
2025-07-13 17:16:57 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 17:16:57 - startup - INFO - ============================================================
2025-07-13 17:16:57 - startup - INFO - 📅 Startup Time: 2025-07-13T17:16:57.118357
2025-07-13 17:16:57 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 17:16:57 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 17:16:57 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 17:16:57 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 17:16:57 - startup - INFO - ============================================================
2025-07-13 17:16:57 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 17:16:57 - engineio.server - INFO - Server initialized for threading.
2025-07-13 17:16:57 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 17:16:57 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 17:16:57 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 17:16:57 - allora - INFO - 🔧 Pool size: 10
2025-07-13 17:16:57 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 17:16:57 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 17:16:57 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 17:16:57 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 17:16:57 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.015s]
2025-07-13 17:16:57 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 17:16:57 - allora - INFO - Search API blueprint registered successfully
2025-07-13 17:16:57 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 17:16:58 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 17:16:58 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 17:16:58 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 17:16:58 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 17:16:58 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 17:16:58 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 17:16:58 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 17:16:58 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 17:16:58 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 17:16:58 - allora - INFO - Recommendation system initialized successfully
2025-07-13 17:16:58 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 17:16:58 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 17:16:58 - tracking_system - INFO - Real-time tracking system started
2025-07-13 17:16:58 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 17:16:58 - notification_service - INFO - Notification delivery service started
2025-07-13 17:16:58 - allora - INFO - Notification service initialized successfully
2025-07-13 17:16:58 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 17:17:00 - engineio.server - INFO - hio5mGnQpwhjonHSAAAP: Sending packet OPEN data {'sid': 'hio5mGnQpwhjonHSAAAP', 'upgrades': ['websocket'], 'pingTimeout': 60000, 'pingInterval': 25000, 'maxPayload': 1000000}
2025-07-13 17:17:00 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 17:17:00] "GET /socket.io/?transport=polling&EIO=4&t=**********.6198072 HTTP/1.1" 200 -
2025-07-13 17:17:02 - engineio.server - INFO - hio5mGnQpwhjonHSAAAP: Received request to upgrade to websocket
2025-07-13 17:17:02 - engineio.server - INFO - hio5mGnQpwhjonHSAAAP: Upgrade to websocket successful
2025-07-13 17:17:02 - engineio.server - INFO - hio5mGnQpwhjonHSAAAP: Received packet MESSAGE data 0{}
2025-07-13 17:17:02 - socketio.server - INFO - 61X6DkwNKG4vhXqjAAAQ is entering room guests [/]
2025-07-13 17:17:02 - flask_socketio_manager - INFO - Guest 61X6DkwNKG4vhXqjAAAQ connected via SocketIO
2025-07-13 17:17:02 - socketio.server - INFO - emitting event "connection_established" to 61X6DkwNKG4vhXqjAAAQ [/]
2025-07-13 17:17:02 - engineio.server - INFO - hio5mGnQpwhjonHSAAAP: Sending packet MESSAGE data 2["connection_established",{"status":"connected","timestamp":"2025-07-13T17:17:02.737022","user_id":null,"session_id":"61X6DkwNKG4vhXqjAAAQ"}]
2025-07-13 17:17:02 - engineio.server - INFO - hio5mGnQpwhjonHSAAAP: Sending packet MESSAGE data 0{"sid":"61X6DkwNKG4vhXqjAAAQ"}
2025-07-13 17:17:04 - socketio.server - INFO - emitting event "inventory_update" to all [/]
2025-07-13 17:17:06 - socketio.server - INFO - emitting event "inventory_update" to all [/]
2025-07-13 17:17:06 - flask_socketio_manager - INFO - Broadcasted inventory update for product 888
2025-07-13 17:17:08 - socketio.server - INFO - emitting event "price_update" to all [/]
2025-07-13 17:17:10 - engineio.server - INFO - hio5mGnQpwhjonHSAAAP: Received packet MESSAGE data 1
2025-07-13 17:17:10 - socketio.server - INFO - 61X6DkwNKG4vhXqjAAAQ is leaving room guests [/]
2025-07-13 17:17:10 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 17:17:10] "GET /socket.io/?transport=websocket&EIO=4&sid=hio5mGnQpwhjonHSAAAP&t=**********.6864517 HTTP/1.1" 200 -
2025-07-13 17:18:14 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 17:18:14 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 17:18:14 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 17:18:14 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 17:18:14 - startup - INFO - ============================================================
2025-07-13 17:18:14 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 17:18:14 - startup - INFO - ============================================================
2025-07-13 17:18:14 - startup - INFO - 📅 Startup Time: 2025-07-13T17:18:14.***********-07-13 17:18:14 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 17:18:14 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 17:18:14 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 17:18:14 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 17:18:14 - startup - INFO - ============================================================
2025-07-13 17:18:14 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 17:18:14 - engineio.server - INFO - Server initialized for threading.
2025-07-13 17:18:14 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 17:18:14 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 17:18:14 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 17:18:14 - allora - INFO - 🔧 Pool size: 10
2025-07-13 17:18:14 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 17:18:14 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 17:18:14 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 17:18:14 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 17:18:14 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.004s]
2025-07-13 17:18:14 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 17:18:14 - allora - INFO - Search API blueprint registered successfully
2025-07-13 17:18:14 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 17:18:15 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 17:18:15 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 17:18:15 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 17:18:15 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 17:18:15 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 17:18:15 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 17:18:15 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 17:18:15 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 17:18:15 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 17:18:15 - allora - INFO - Recommendation system initialized successfully
2025-07-13 17:18:15 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 17:18:15 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 17:18:15 - tracking_system - INFO - Real-time tracking system started
2025-07-13 17:18:15 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 17:18:15 - notification_service - INFO - Notification delivery service started
2025-07-13 17:18:15 - allora - INFO - Notification service initialized successfully
2025-07-13 17:18:15 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 17:18:15 - socketio.server - INFO - emitting event "test_manual_broadcast" to all [/]
2025-07-13 17:18:17 - engineio.server - INFO - yuZC0jlzgjGNlXY2AAAR: Sending packet OPEN data {'sid': 'yuZC0jlzgjGNlXY2AAAR', 'upgrades': ['websocket'], 'pingTimeout': 60000, 'pingInterval': 25000, 'maxPayload': 1000000}
2025-07-13 17:18:17 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 17:18:17] "GET /socket.io/?transport=polling&EIO=4&t=1752407295.6612904 HTTP/1.1" 200 -
2025-07-13 17:18:19 - engineio.server - INFO - yuZC0jlzgjGNlXY2AAAR: Received request to upgrade to websocket
2025-07-13 17:18:19 - engineio.server - INFO - yuZC0jlzgjGNlXY2AAAR: Upgrade to websocket successful
2025-07-13 17:18:19 - engineio.server - INFO - yuZC0jlzgjGNlXY2AAAR: Received packet MESSAGE data 0{}
2025-07-13 17:18:19 - socketio.server - INFO - rsu3Dq-EA8j1kltcAAAS is entering room guests [/]
2025-07-13 17:18:19 - flask_socketio_manager - INFO - Guest rsu3Dq-EA8j1kltcAAAS connected via SocketIO
2025-07-13 17:18:19 - socketio.server - INFO - emitting event "connection_established" to rsu3Dq-EA8j1kltcAAAS [/]
2025-07-13 17:18:19 - engineio.server - INFO - yuZC0jlzgjGNlXY2AAAR: Sending packet MESSAGE data 2["connection_established",{"status":"connected","timestamp":"2025-07-13T17:18:19.800579","user_id":null,"session_id":"rsu3Dq-EA8j1kltcAAAS"}]
2025-07-13 17:18:19 - engineio.server - INFO - yuZC0jlzgjGNlXY2AAAR: Sending packet MESSAGE data 0{"sid":"rsu3Dq-EA8j1kltcAAAS"}
2025-07-13 17:18:21 - engineio.server - INFO - yuZC0jlzgjGNlXY2AAAR: Received packet MESSAGE data 2["test_broadcast_request",{"type":"inventory_update","product_id":123,"new_quantity":50}]
2025-07-13 17:18:21 - socketio.server - INFO - received event "test_broadcast_request" from rsu3Dq-EA8j1kltcAAAS [/]
2025-07-13 17:18:23 - engineio.server - INFO - yuZC0jlzgjGNlXY2AAAR: Received packet MESSAGE data 2["ping"]
2025-07-13 17:18:23 - socketio.server - INFO - received event "ping" from rsu3Dq-EA8j1kltcAAAS [/]
2025-07-13 17:18:23 - socketio.server - INFO - emitting event "pong" to rsu3Dq-EA8j1kltcAAAS [/]
2025-07-13 17:18:23 - engineio.server - INFO - yuZC0jlzgjGNlXY2AAAR: Sending packet MESSAGE data 2["pong",{"timestamp":"2025-07-13T17:18:23.831588"}]
2025-07-13 17:18:25 - engineio.server - INFO - yuZC0jlzgjGNlXY2AAAR: Received packet MESSAGE data 1
2025-07-13 17:18:25 - socketio.server - INFO - rsu3Dq-EA8j1kltcAAAS is leaving room guests [/]
2025-07-13 17:18:25 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 17:18:25] "GET /socket.io/?transport=websocket&EIO=4&sid=yuZC0jlzgjGNlXY2AAAR&t=1752407297.747207 HTTP/1.1" 200 -
2025-07-13 17:19:05 - engineio.server - INFO - vb2LwnC5q3wt6pV-AAAT: Sending packet OPEN data {'sid': 'vb2LwnC5q3wt6pV-AAAT', 'upgrades': ['websocket'], 'pingTimeout': 60000, 'pingInterval': 25000, 'maxPayload': 1000000}
2025-07-13 17:19:05 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 17:19:05] "GET /socket.io/?transport=polling&EIO=4&t=1752407343.4119651 HTTP/1.1" 200 -
2025-07-13 17:19:07 - engineio.server - INFO - vb2LwnC5q3wt6pV-AAAT: Received request to upgrade to websocket
2025-07-13 17:19:07 - engineio.server - INFO - vb2LwnC5q3wt6pV-AAAT: Upgrade to websocket successful
2025-07-13 17:19:07 - engineio.server - INFO - vb2LwnC5q3wt6pV-AAAT: Received packet MESSAGE data 0{}
2025-07-13 17:19:07 - socketio.server - INFO - QTGDT6pWfilR9RYWAAAU is entering room guests [/]
2025-07-13 17:19:07 - flask_socketio_manager - INFO - Guest QTGDT6pWfilR9RYWAAAU connected via SocketIO
2025-07-13 17:19:07 - socketio.server - INFO - emitting event "connection_established" to QTGDT6pWfilR9RYWAAAU [/]
2025-07-13 17:19:07 - engineio.server - INFO - vb2LwnC5q3wt6pV-AAAT: Sending packet MESSAGE data 2["connection_established",{"status":"connected","timestamp":"2025-07-13T17:19:07.538407","user_id":null,"session_id":"QTGDT6pWfilR9RYWAAAU"}]
2025-07-13 17:19:07 - engineio.server - INFO - vb2LwnC5q3wt6pV-AAAT: Sending packet MESSAGE data 0{"sid":"QTGDT6pWfilR9RYWAAAU"}
2025-07-13 17:19:08 - engineio.server - INFO - vb2LwnC5q3wt6pV-AAAT: Received packet MESSAGE data 2["subscribe",{"events":["inventory","prices","orders","notifications","admin"]}]
2025-07-13 17:19:08 - socketio.server - INFO - received event "subscribe" from QTGDT6pWfilR9RYWAAAU [/]
2025-07-13 17:19:08 - socketio.server - INFO - QTGDT6pWfilR9RYWAAAU is entering room events_inventory [/]
2025-07-13 17:19:08 - socketio.server - INFO - QTGDT6pWfilR9RYWAAAU is entering room events_prices [/]
2025-07-13 17:19:08 - socketio.server - INFO - QTGDT6pWfilR9RYWAAAU is entering room events_orders [/]
2025-07-13 17:19:08 - socketio.server - INFO - emitting event "subscribed" to QTGDT6pWfilR9RYWAAAU [/]
2025-07-13 17:19:08 - engineio.server - INFO - vb2LwnC5q3wt6pV-AAAT: Sending packet MESSAGE data 2["subscribed",{"events":["inventory","prices","orders","notifications","admin"],"timestamp":"2025-07-13T17:19:08.551390"}]
2025-07-13 17:19:17 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 17:19:17 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 17:19:17 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 17:19:17 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 17:19:17 - startup - INFO - ============================================================
2025-07-13 17:19:17 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 17:19:17 - startup - INFO - ============================================================
2025-07-13 17:19:17 - startup - INFO - 📅 Startup Time: 2025-07-13T17:19:17.560267
2025-07-13 17:19:17 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 17:19:17 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 17:19:17 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 17:19:17 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 17:19:17 - startup - INFO - ============================================================
2025-07-13 17:19:17 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 17:19:17 - engineio.server - INFO - Server initialized for threading.
2025-07-13 17:19:17 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 17:19:17 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 17:19:17 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 17:19:17 - allora - INFO - 🔧 Pool size: 10
2025-07-13 17:19:17 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 17:19:17 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 17:19:17 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 17:19:17 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 17:19:18 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.033s]
2025-07-13 17:19:18 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 17:19:18 - allora - INFO - Search API blueprint registered successfully
2025-07-13 17:19:18 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 17:19:18 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 17:19:18 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 17:19:18 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 17:19:18 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 17:19:18 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 17:19:18 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 17:19:18 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 17:19:18 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 17:19:18 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 17:19:18 - allora - INFO - Recommendation system initialized successfully
2025-07-13 17:19:18 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 17:19:18 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 17:19:18 - tracking_system - INFO - Real-time tracking system started
2025-07-13 17:19:18 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 17:19:18 - notification_service - INFO - Notification delivery service started
2025-07-13 17:19:18 - allora - INFO - Notification service initialized successfully
2025-07-13 17:19:18 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 17:19:19 - socketio.server - INFO - emitting event "inventory_update" to all [/]
2025-07-13 17:19:19 - flask_socketio_manager - INFO - Broadcasted inventory update for product 123
2025-07-13 17:19:22 - socketio.server - INFO - emitting event "price_update" to all [/]
2025-07-13 17:19:22 - flask_socketio_manager - INFO - Broadcasted price update for product 456
2025-07-13 17:19:24 - socketio.server - INFO - emitting event "cart_update" to user_test_user_123 [/]
2025-07-13 17:19:24 - flask_socketio_manager - INFO - Sent cart update to user test_user_123
2025-07-13 17:19:26 - socketio.server - INFO - emitting event "notification" to user_test_user_123 [/]
2025-07-13 17:19:26 - flask_socketio_manager - INFO - Sent notification to user test_user_123
2025-07-13 17:19:28 - socketio.server - INFO - emitting event "admin_notification" to admin [/]
2025-07-13 17:19:28 - flask_socketio_manager - INFO - Broadcasted message to admin users
2025-07-13 17:19:30 - engineio.server - INFO - vb2LwnC5q3wt6pV-AAAT: Received packet MESSAGE data 1
2025-07-13 17:19:30 - socketio.server - INFO - QTGDT6pWfilR9RYWAAAU is leaving room guests [/]
2025-07-13 17:19:30 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 17:19:30] "GET /socket.io/?transport=websocket&EIO=4&sid=vb2LwnC5q3wt6pV-AAAT&t=1752407345.4763892 HTTP/1.1" 200 -
2025-07-13 17:21:03 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 17:21:03] "GET /socketio-test HTTP/1.1" 200 -
2025-07-13 17:21:04 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 17:21:04] "GET /socketio-test HTTP/1.1" 200 -
2025-07-13 17:21:23 - allora - WARNING - 404 error for path: /api/
2025-07-13 17:21:23 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 17:21:23] "[33mGET /api/ HTTP/1.1[0m" 404 -
2025-07-13 18:04:16 - allora - WARNING - 404 error for path: /api/
2025-07-13 18:04:16 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 18:04:16] "[33mGET /api/ HTTP/1.1[0m" 404 -
2025-07-13 18:04:22 - allora - ERROR - Unhandled exception: MethodNotAllowed at None
2025-07-13 18:04:22 - allora - ERROR - Unhandled exception: 405 Method Not Allowed: The method is not allowed for the requested URL.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1458, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1440, in raise_routing_exception
    raise request.routing_exception  # type: ignore
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\ctx.py", line 353, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\werkzeug\routing\map.py", line 624, in match
    raise MethodNotAllowed(valid_methods=list(e.have_match_for)) from None
werkzeug.exceptions.MethodNotAllowed: 405 Method Not Allowed: The method is not allowed for the requested URL.
2025-07-13 18:04:22 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 18:04:22] "[35m[1mGET /api/webhooks/blue-dart HTTP/1.1[0m" 500 -
2025-07-13 18:04:37 - allora - ERROR - Unhandled exception: MethodNotAllowed at None
2025-07-13 18:04:37 - allora - ERROR - Unhandled exception: 405 Method Not Allowed: The method is not allowed for the requested URL.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1458, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1440, in raise_routing_exception
    raise request.routing_exception  # type: ignore
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\ctx.py", line 353, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\werkzeug\routing\map.py", line 624, in match
    raise MethodNotAllowed(valid_methods=list(e.have_match_for)) from None
werkzeug.exceptions.MethodNotAllowed: 405 Method Not Allowed: The method is not allowed for the requested URL.
2025-07-13 18:04:37 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 18:04:37] "[35m[1mGET /api/webhooks/delhivery HTTP/1.1[0m" 500 -
2025-07-13 18:15:57 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 18:15:57 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 18:15:57 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 18:15:57 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 18:15:57 - startup - INFO - ============================================================
2025-07-13 18:15:57 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 18:15:57 - startup - INFO - ============================================================
2025-07-13 18:15:57 - startup - INFO - 📅 Startup Time: 2025-07-13T18:15:57.674834
2025-07-13 18:15:57 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 18:15:57 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 18:15:57 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 18:15:57 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 18:15:57 - startup - INFO - ============================================================
2025-07-13 18:15:57 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 18:15:57 - engineio.server - INFO - Server initialized for threading.
2025-07-13 18:15:57 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 18:15:57 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 18:15:57 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 18:15:57 - allora - INFO - 🔧 Pool size: 10
2025-07-13 18:15:57 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 18:15:57 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 18:15:57 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 18:15:57 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 18:15:58 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.023s]
2025-07-13 18:15:58 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 18:15:58 - allora - INFO - Search API blueprint registered successfully
2025-07-13 18:15:58 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 18:15:58 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 18:15:58 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 18:15:58 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 18:15:58 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 18:15:58 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 18:15:58 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 18:15:58 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 18:15:58 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 18:15:58 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 18:15:58 - allora - INFO - Recommendation system initialized successfully
2025-07-13 18:15:58 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 18:15:58 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 18:15:58 - tracking_system - INFO - Real-time tracking system started
2025-07-13 18:15:58 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 18:15:58 - notification_service - INFO - Notification delivery service started
2025-07-13 18:15:58 - allora - INFO - Notification service initialized successfully
2025-07-13 18:15:58 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 18:15:59 - tracking_dashboard - ERROR - Error getting basic metrics: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:15:59 - tracking_dashboard - ERROR - Error getting carrier performance: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:15:59 - tracking_dashboard - ERROR - Error getting daily volume: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:15:59 - tracking_dashboard - ERROR - Error getting status distribution: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:15:59 - tracking_dashboard - ERROR - Error getting recent exceptions: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:15:59 - tracking_dashboard - ERROR - Error getting shipment list: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:17:31 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 18:17:31 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 18:17:31 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 18:17:31 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 18:17:31 - startup - INFO - ============================================================
2025-07-13 18:17:31 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 18:17:31 - startup - INFO - ============================================================
2025-07-13 18:17:31 - startup - INFO - 📅 Startup Time: 2025-07-13T18:17:31.032144
2025-07-13 18:17:31 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 18:17:31 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 18:17:31 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 18:17:31 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 18:17:31 - startup - INFO - ============================================================
2025-07-13 18:17:31 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 18:17:31 - engineio.server - INFO - Server initialized for threading.
2025-07-13 18:17:31 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 18:17:31 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 18:17:31 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 18:17:31 - allora - INFO - 🔧 Pool size: 10
2025-07-13 18:17:31 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 18:17:31 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 18:17:31 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 18:17:31 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 18:17:31 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.015s]
2025-07-13 18:17:31 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 18:17:31 - allora - INFO - Search API blueprint registered successfully
2025-07-13 18:17:31 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 18:17:31 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 18:17:31 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 18:17:31 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 18:17:31 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 18:17:32 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 18:17:32 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 18:17:32 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 18:17:32 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 18:17:32 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 18:17:32 - allora - INFO - Recommendation system initialized successfully
2025-07-13 18:17:32 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 18:17:32 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 18:17:32 - tracking_system - INFO - Real-time tracking system started
2025-07-13 18:17:32 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 18:17:32 - notification_service - INFO - Notification delivery service started
2025-07-13 18:17:32 - allora - INFO - Notification service initialized successfully
2025-07-13 18:17:32 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 18:17:32 - tracking_dashboard - ERROR - Error getting basic metrics: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:17:32 - tracking_dashboard - ERROR - Error getting carrier performance: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:17:32 - tracking_dashboard - ERROR - Error getting daily volume: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:17:32 - tracking_dashboard - ERROR - Error getting status distribution: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:17:32 - tracking_dashboard - ERROR - Error getting recent exceptions: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:23:56 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 18:23:56 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 18:23:56 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 18:23:56 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 18:23:56 - startup - INFO - ============================================================
2025-07-13 18:23:56 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 18:23:56 - startup - INFO - ============================================================
2025-07-13 18:23:56 - startup - INFO - 📅 Startup Time: 2025-07-13T18:23:56.770102
2025-07-13 18:23:56 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 18:23:56 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 18:23:56 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 18:23:56 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 18:23:56 - startup - INFO - ============================================================
2025-07-13 18:23:56 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 18:23:56 - engineio.server - INFO - Server initialized for threading.
2025-07-13 18:23:56 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 18:23:56 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 18:23:57 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 18:23:57 - allora - INFO - 🔧 Pool size: 10
2025-07-13 18:23:57 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 18:23:57 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 18:23:57 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 18:23:57 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 18:23:57 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.025s]
2025-07-13 18:23:57 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 18:23:57 - allora - INFO - Search API blueprint registered successfully
2025-07-13 18:23:57 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 18:23:57 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 18:23:57 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 18:23:57 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 18:23:57 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 18:23:57 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 18:23:57 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 18:23:57 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 18:23:57 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 18:23:57 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 18:23:57 - allora - INFO - Recommendation system initialized successfully
2025-07-13 18:23:57 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 18:23:57 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 18:23:57 - tracking_system - INFO - Real-time tracking system started
2025-07-13 18:23:57 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 18:23:57 - notification_service - INFO - Notification delivery service started
2025-07-13 18:23:57 - allora - INFO - Notification service initialized successfully
2025-07-13 18:23:57 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 18:24:30 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 18:24:30 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 18:24:30 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 18:24:30 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 18:24:30 - startup - INFO - ============================================================
2025-07-13 18:24:30 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 18:24:30 - startup - INFO - ============================================================
2025-07-13 18:24:30 - startup - INFO - 📅 Startup Time: 2025-07-13T18:24:30.707452
2025-07-13 18:24:30 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 18:24:30 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 18:24:30 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 18:24:30 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 18:24:30 - startup - INFO - ============================================================
2025-07-13 18:24:30 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 18:24:30 - engineio.server - INFO - Server initialized for threading.
2025-07-13 18:24:30 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 18:24:30 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 18:24:30 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 18:24:30 - allora - INFO - 🔧 Pool size: 10
2025-07-13 18:24:30 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 18:24:30 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 18:24:30 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 18:24:30 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 18:24:31 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.022s]
2025-07-13 18:24:31 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 18:24:31 - allora - INFO - Search API blueprint registered successfully
2025-07-13 18:24:31 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 18:24:31 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 18:24:31 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 18:24:31 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 18:24:31 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 18:24:31 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 18:24:31 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 18:24:31 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 18:24:31 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 18:24:31 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 18:24:31 - allora - INFO - Recommendation system initialized successfully
2025-07-13 18:24:31 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 18:24:31 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 18:24:31 - tracking_system - INFO - Real-time tracking system started
2025-07-13 18:24:31 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 18:24:31 - notification_service - INFO - Notification delivery service started
2025-07-13 18:24:31 - allora - INFO - Notification service initialized successfully
2025-07-13 18:24:31 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 18:56:10 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 18:56:10 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 18:56:10 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 18:56:10 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 18:56:10 - startup - INFO - ============================================================
2025-07-13 18:56:10 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 18:56:10 - startup - INFO - ============================================================
2025-07-13 18:56:10 - startup - INFO - 📅 Startup Time: 2025-07-13T18:56:10.158154
2025-07-13 18:56:10 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 18:56:10 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 18:56:10 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 18:56:10 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 18:56:10 - startup - INFO - ============================================================
2025-07-13 18:56:10 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 18:56:10 - engineio.server - INFO - Server initialized for threading.
2025-07-13 18:56:10 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 18:56:10 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 18:56:10 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 18:56:10 - allora - INFO - 🔧 Pool size: 10
2025-07-13 18:56:10 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 18:56:10 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 18:56:10 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 18:56:10 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 18:56:10 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.028s]
2025-07-13 18:56:10 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 18:56:10 - allora - INFO - Search API blueprint registered successfully
2025-07-13 18:56:10 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 18:56:11 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 18:56:11 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 18:56:11 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 18:56:11 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 18:56:11 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 18:56:11 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 18:56:11 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 18:56:11 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 18:56:11 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 18:56:11 - allora - INFO - Recommendation system initialized successfully
2025-07-13 18:56:11 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 18:56:11 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 18:56:11 - tracking_system - INFO - Real-time tracking system started
2025-07-13 18:56:11 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 18:56:11 - notification_service - INFO - Notification delivery service started
2025-07-13 18:56:11 - allora - INFO - Notification service initialized successfully
2025-07-13 18:56:11 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 18:56:11 - order_fulfillment_engine - INFO - Order Fulfillment Engine initialized
2025-07-13 18:56:12 - fulfillment_rules - INFO - Loaded 11 default fulfillment rules
2025-07-13 19:13:04 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 19:13:04 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 19:13:04 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 19:13:04 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 19:13:04 - startup - INFO - ============================================================
2025-07-13 19:13:04 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 19:13:04 - startup - INFO - ============================================================
2025-07-13 19:13:04 - startup - INFO - 📅 Startup Time: 2025-07-13T19:13:04.060656
2025-07-13 19:13:04 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 19:13:04 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 19:13:04 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 19:13:04 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 19:13:04 - startup - INFO - ============================================================
2025-07-13 19:13:04 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 19:13:04 - engineio.server - INFO - Server initialized for threading.
2025-07-13 19:13:04 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 19:13:04 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 19:13:04 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 19:13:04 - allora - INFO - 🔧 Pool size: 10
2025-07-13 19:13:04 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 19:13:04 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 19:13:04 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 19:13:04 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 19:13:04 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.022s]
2025-07-13 19:13:04 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 19:13:04 - allora - INFO - Search API blueprint registered successfully
2025-07-13 19:13:04 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 19:13:05 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 19:13:05 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 19:13:05 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 19:13:05 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 19:13:05 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 19:13:05 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 19:13:05 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 19:13:05 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 19:13:05 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 19:13:05 - allora - INFO - Recommendation system initialized successfully
2025-07-13 19:13:05 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 19:13:05 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 19:13:05 - tracking_system - INFO - Real-time tracking system started
2025-07-13 19:13:05 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 19:13:05 - notification_service - INFO - Notification delivery service started
2025-07-13 19:13:05 - allora - INFO - Notification service initialized successfully
2025-07-13 19:13:05 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 19:13:05 - order_fulfillment.order_fulfillment_engine - INFO - Order Fulfillment Engine initialized
2025-07-13 19:15:13 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 19:15:13 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 19:15:13 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 19:15:13 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 19:15:13 - startup - INFO - ============================================================
2025-07-13 19:15:13 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 19:15:13 - startup - INFO - ============================================================
2025-07-13 19:15:13 - startup - INFO - 📅 Startup Time: 2025-07-13T19:15:13.584847
2025-07-13 19:15:13 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 19:15:13 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 19:15:13 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 19:15:13 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 19:15:13 - startup - INFO - ============================================================
2025-07-13 19:15:13 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 19:15:13 - engineio.server - INFO - Server initialized for threading.
2025-07-13 19:15:13 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 19:15:13 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 19:15:13 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 19:15:13 - allora - INFO - 🔧 Pool size: 10
2025-07-13 19:15:13 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 19:15:13 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 19:15:13 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 19:15:13 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 19:15:14 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.017s]
2025-07-13 19:15:14 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 19:15:14 - allora - INFO - Search API blueprint registered successfully
2025-07-13 19:15:14 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 19:15:14 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 19:15:14 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 19:15:14 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 19:15:14 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 19:15:14 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 19:15:14 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 19:15:14 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 19:15:14 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 19:15:14 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 19:15:14 - allora - INFO - Recommendation system initialized successfully
2025-07-13 19:15:14 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 19:15:14 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 19:15:14 - tracking_system - INFO - Real-time tracking system started
2025-07-13 19:15:14 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 19:15:14 - notification_service - INFO - Notification delivery service started
2025-07-13 19:15:14 - allora - INFO - Notification service initialized successfully
2025-07-13 19:15:14 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 19:15:14 - order_fulfillment.order_fulfillment_engine - INFO - Order Fulfillment Engine initialized
2025-07-13 19:15:15 - order_fulfillment.fulfillment_rules - INFO - Loaded 11 default fulfillment rules
2025-07-13 19:43:29 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 19:43:29 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 19:43:29 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 19:43:29 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 19:43:29 - startup - INFO - ============================================================
2025-07-13 19:43:29 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 19:43:29 - startup - INFO - ============================================================
2025-07-13 19:43:29 - startup - INFO - 📅 Startup Time: 2025-07-13T19:43:29.869786
2025-07-13 19:43:29 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 19:43:29 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 19:43:29 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 19:43:29 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 19:43:29 - startup - INFO - ============================================================
2025-07-13 19:43:29 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 19:43:29 - engineio.server - INFO - Server initialized for threading.
2025-07-13 19:43:29 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 19:43:29 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 19:43:30 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 19:43:30 - allora - INFO - 🔧 Pool size: 10
2025-07-13 19:43:30 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 19:43:30 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 19:43:30 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 19:43:30 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 19:43:30 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.022s]
2025-07-13 19:43:30 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 19:43:30 - allora - INFO - Search API blueprint registered successfully
2025-07-13 19:43:30 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 19:43:31 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 19:43:31 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 19:43:31 - order_fulfillment.fulfillment_rules - INFO - Loaded 11 default fulfillment rules
2025-07-13 19:43:31 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 19:43:31 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 19:43:31 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 19:43:31 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 19:43:31 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 19:43:31 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 19:43:31 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 19:43:31 - allora - INFO - Recommendation system initialized successfully
2025-07-13 19:43:31 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 19:43:31 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 19:43:31 - tracking_system - INFO - Real-time tracking system started
2025-07-13 19:43:31 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 19:43:31 - notification_service - INFO - Notification delivery service started
2025-07-13 19:43:31 - allora - INFO - Notification service initialized successfully
2025-07-13 19:43:31 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 19:43:31 - scheduler_init - INFO - Registered scheduler: inventory_sync
2025-07-13 19:43:31 - scheduler_init - INFO - Inventory scheduler registered successfully
2025-07-13 19:44:10 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 19:44:10 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 19:44:10 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 19:44:10 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 19:44:10 - startup - INFO - ============================================================
2025-07-13 19:44:10 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 19:44:10 - startup - INFO - ============================================================
2025-07-13 19:44:10 - startup - INFO - 📅 Startup Time: 2025-07-13T19:44:10.313179
2025-07-13 19:44:10 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 19:44:10 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 19:44:10 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 19:44:10 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 19:44:10 - startup - INFO - ============================================================
2025-07-13 19:44:10 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 19:44:10 - engineio.server - INFO - Server initialized for threading.
2025-07-13 19:44:10 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 19:44:10 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 19:44:10 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 19:44:10 - allora - INFO - 🔧 Pool size: 10
2025-07-13 19:44:10 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 19:44:10 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 19:44:10 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 19:44:10 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 19:44:10 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.005s]
2025-07-13 19:44:10 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 19:44:10 - allora - INFO - Search API blueprint registered successfully
2025-07-13 19:44:10 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 19:44:11 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 19:44:11 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 19:44:11 - order_fulfillment.fulfillment_rules - INFO - Loaded 11 default fulfillment rules
2025-07-13 19:44:11 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 19:44:11 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 19:44:11 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 19:44:11 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 19:44:11 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 19:44:11 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 19:44:11 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 19:44:11 - allora - INFO - Recommendation system initialized successfully
2025-07-13 19:44:11 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 19:44:11 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 19:44:11 - tracking_system - INFO - Real-time tracking system started
2025-07-13 19:44:11 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 19:44:11 - notification_service - INFO - Notification delivery service started
2025-07-13 19:44:11 - allora - INFO - Notification service initialized successfully
2025-07-13 19:44:11 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 19:44:11 - scheduler_init - INFO - Registered scheduler: inventory_sync
2025-07-13 19:44:11 - scheduler_init - INFO - Inventory scheduler registered successfully
2025-07-13 19:52:11 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 19:52:11 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 19:52:11 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 19:52:11 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 19:52:11 - startup - INFO - ============================================================
2025-07-13 19:52:11 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 19:52:11 - startup - INFO - ============================================================
2025-07-13 19:52:11 - startup - INFO - 📅 Startup Time: 2025-07-13T19:52:11.388884
2025-07-13 19:52:11 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 19:52:11 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 19:52:11 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 19:52:11 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 19:52:11 - startup - INFO - ============================================================
2025-07-13 19:52:11 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 19:52:11 - engineio.server - INFO - Server initialized for threading.
2025-07-13 19:52:11 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 19:52:11 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 19:52:11 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 19:52:11 - allora - INFO - 🔧 Pool size: 10
2025-07-13 19:52:11 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 19:52:11 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 19:52:11 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 19:52:11 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 19:52:11 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.022s]
2025-07-13 19:52:11 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 19:52:11 - allora - INFO - Search API blueprint registered successfully
2025-07-13 19:52:11 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 19:52:12 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 19:52:12 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 19:52:12 - order_fulfillment.fulfillment_rules - INFO - Loaded 11 default fulfillment rules
2025-07-13 19:52:12 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 19:52:12 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 19:52:12 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 19:52:12 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 19:52:12 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 19:52:12 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 19:52:12 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 19:52:12 - allora - INFO - Recommendation system initialized successfully
2025-07-13 19:52:12 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 19:52:12 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 19:52:12 - tracking_system - INFO - Real-time tracking system started
2025-07-13 19:52:12 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 19:52:12 - notification_service - INFO - Notification delivery service started
2025-07-13 19:52:12 - allora - INFO - Notification service initialized successfully
2025-07-13 19:52:12 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting recent community posts: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:52:13 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 19:52:13] "[35m[1mGET /api/community-highlights/recent-posts HTTP/1.1[0m" 500 -
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting recent community posts: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:52:13 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 19:52:13] "[35m[1mGET /api/community-highlights/recent-posts?limit=6&type=all HTTP/1.1[0m" 500 -
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting sustainability stories: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:52:13 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 19:52:13] "[35m[1mGET /api/community-highlights/sustainability-stories HTTP/1.1[0m" 500 -
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting sustainability stories: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:52:13 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 19:52:13] "[35m[1mGET /api/community-highlights/sustainability-stories?limit=4 HTTP/1.1[0m" 500 -
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting featured eco brands: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:52:13 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 19:52:13] "[35m[1mGET /api/community-highlights/featured-brands HTTP/1.1[0m" 500 -
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting featured eco brands: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:52:13 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 19:52:13] "[35m[1mGET /api/community-highlights/featured-brands?limit=3 HTTP/1.1[0m" 500 -
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting recent reviews: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:52:13 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 19:52:13] "[35m[1mGET /api/community-highlights/recent-reviews HTTP/1.1[0m" 500 -
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting recent reviews: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:52:13 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 19:52:13] "[35m[1mGET /api/community-highlights/recent-reviews?limit=4 HTTP/1.1[0m" 500 -
2025-07-13 19:57:22 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 19:57:22 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 19:57:22 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 19:57:22 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 19:57:22 - startup - INFO - ============================================================
2025-07-13 19:57:22 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 19:57:22 - startup - INFO - ============================================================
2025-07-13 19:57:22 - startup - INFO - 📅 Startup Time: 2025-07-13T19:57:22.162444
2025-07-13 19:57:22 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 19:57:22 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 19:57:22 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 19:57:22 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 19:57:22 - startup - INFO - ============================================================
2025-07-13 19:57:22 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 19:57:22 - engineio.server - INFO - Server initialized for threading.
2025-07-13 19:57:22 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 19:57:22 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 19:57:22 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 19:57:22 - allora - INFO - 🔧 Pool size: 10
2025-07-13 19:57:22 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 19:57:22 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 19:57:22 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 19:57:22 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 19:57:22 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.019s]
2025-07-13 19:57:22 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 19:57:22 - allora - INFO - Search API blueprint registered successfully
2025-07-13 19:57:22 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 19:57:23 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 19:57:23 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 19:57:23 - order_fulfillment.fulfillment_rules - INFO - Loaded 11 default fulfillment rules
2025-07-13 19:57:23 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 19:57:23 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 19:57:23 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 19:57:23 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 19:57:23 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 19:57:23 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 19:57:23 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 19:57:23 - allora - INFO - Recommendation system initialized successfully
2025-07-13 19:57:23 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 19:57:23 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 19:57:23 - tracking_system - INFO - Real-time tracking system started
2025-07-13 19:57:23 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 19:57:23 - notification_service - INFO - Notification delivery service started
2025-07-13 19:57:23 - allora - INFO - Notification service initialized successfully
2025-07-13 19:57:23 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 19:57:23 - community_highlights_api - ERROR - Error getting recent community posts: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:23 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 19:57:23] "[35m[1mGET /api/community-highlights/recent-posts HTTP/1.1[0m" 500 -
2025-07-13 19:57:23 - community_highlights_api - ERROR - Error getting recent community posts: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:23 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 19:57:23] "[35m[1mGET /api/community-highlights/recent-posts?limit=6&type=all HTTP/1.1[0m" 500 -
2025-07-13 19:57:23 - community_highlights_api - ERROR - Error getting sustainability stories: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:23 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 19:57:23] "[35m[1mGET /api/community-highlights/sustainability-stories HTTP/1.1[0m" 500 -
2025-07-13 19:57:23 - community_highlights_api - ERROR - Error getting sustainability stories: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:23 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 19:57:23] "[35m[1mGET /api/community-highlights/sustainability-stories?limit=4 HTTP/1.1[0m" 500 -
2025-07-13 19:57:23 - community_highlights_api - ERROR - Error getting featured eco brands: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:23 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 19:57:23] "[35m[1mGET /api/community-highlights/featured-brands HTTP/1.1[0m" 500 -
2025-07-13 19:57:23 - community_highlights_api - ERROR - Error getting featured eco brands: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:23 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 19:57:23] "[35m[1mGET /api/community-highlights/featured-brands?limit=3 HTTP/1.1[0m" 500 -
2025-07-13 19:57:24 - community_highlights_api - ERROR - Error getting recent reviews: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:24 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 19:57:24] "[35m[1mGET /api/community-highlights/recent-reviews HTTP/1.1[0m" 500 -
2025-07-13 19:57:24 - community_highlights_api - ERROR - Error getting recent reviews: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:24 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 19:57:24] "[35m[1mGET /api/community-highlights/recent-reviews?limit=4 HTTP/1.1[0m" 500 -
2025-07-13 20:24:13 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 20:24:13 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 20:24:13 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 20:24:13 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 20:24:13 - startup - INFO - ============================================================
2025-07-13 20:24:13 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 20:24:13 - startup - INFO - ============================================================
2025-07-13 20:24:13 - startup - INFO - 📅 Startup Time: 2025-07-13T20:24:13.975600
2025-07-13 20:24:13 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 20:24:13 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 20:24:13 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 20:24:13 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 20:24:13 - startup - INFO - ============================================================
2025-07-13 20:24:13 - allora - INFO - 🔧 Using configuration: ProductionConfig
2025-07-13 20:24:13 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 20:24:13 - allora - INFO - ✅ Centralized configuration applied
2025-07-13 20:24:14 - engineio.server - INFO - Server initialized for threading.
2025-07-13 20:24:14 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 20:24:14 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 20:24:14 - allora - INFO - ✅ CORS configured with origins: ['http://localhost:3000', 'http://localhost:3001']
2025-07-13 20:24:14 - allora - INFO - ✅ JWT configuration applied from centralized config
2025-07-13 20:24:14 - allora - INFO - ✅ SECRET_KEY configuration applied from centralized config
2025-07-13 20:24:14 - allora - INFO - ✅ Database configuration applied from centralized config
2025-07-13 20:24:14 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 20:24:14 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 20:24:14 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.027s]
2025-07-13 20:24:14 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 20:24:14 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.018s]
2025-07-13 20:24:14 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 20:25:22 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 20:25:22 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 20:25:22 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 20:25:22 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 20:25:22 - startup - INFO - ============================================================
2025-07-13 20:25:22 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 20:25:22 - startup - INFO - ============================================================
2025-07-13 20:25:22 - startup - INFO - 📅 Startup Time: 2025-07-13T20:25:22.637045
2025-07-13 20:25:22 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 20:25:22 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 20:25:22 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 20:25:22 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 20:25:22 - startup - INFO - ============================================================
2025-07-13 20:25:22 - allora - INFO - 🔧 Using configuration: ProductionConfig
2025-07-13 20:25:22 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 20:25:22 - allora - INFO - ✅ Centralized configuration applied
2025-07-13 20:25:22 - engineio.server - INFO - Server initialized for threading.
2025-07-13 20:25:22 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 20:25:22 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 20:25:22 - allora - INFO - ✅ CORS configured with origins: ['http://localhost:3000', 'http://localhost:3001']
2025-07-13 20:25:22 - allora - INFO - ✅ JWT configuration applied from centralized config
2025-07-13 20:25:22 - allora - INFO - ✅ SECRET_KEY configuration applied from centralized config
2025-07-13 20:25:22 - allora - INFO - ✅ Database configuration applied from centralized config
2025-07-13 20:25:22 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 20:25:22 - allora - INFO - 🔧 Pool size: 10
2025-07-13 20:25:22 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 20:25:22 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 20:25:22 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 20:25:22 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 20:25:23 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.017s]
2025-07-13 20:25:23 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 20:25:23 - allora - INFO - Search API blueprint registered successfully
2025-07-13 20:25:23 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 20:25:25 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 20:25:25 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 20:25:25 - order_fulfillment.fulfillment_rules - INFO - Loaded 11 default fulfillment rules
2025-07-13 20:25:25 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 20:25:25 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 20:25:25 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 20:25:25 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 20:25:25 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 20:25:25 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 20:25:25 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 20:25:25 - allora - INFO - Recommendation system initialized successfully
2025-07-13 20:25:25 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 20:25:25 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 20:25:25 - tracking_system - INFO - Real-time tracking system started
2025-07-13 20:25:25 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 20:25:25 - notification_service - INFO - Notification delivery service started
2025-07-13 20:25:25 - allora - INFO - Notification service initialized successfully
2025-07-13 20:25:25 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 20:25:26 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.026s]
2025-07-13 20:25:26 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 20:27:18 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 20:27:18 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 20:27:18 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 20:27:18 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 20:27:18 - startup - INFO - ============================================================
2025-07-13 20:27:18 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 20:27:18 - startup - INFO - ============================================================
2025-07-13 20:27:18 - startup - INFO - 📅 Startup Time: 2025-07-13T20:27:18.220204
2025-07-13 20:27:18 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 20:27:18 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 20:27:18 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 20:27:18 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 20:27:18 - startup - INFO - ============================================================
2025-07-13 20:27:18 - allora - INFO - 🔧 Using configuration: ProductionConfig
2025-07-13 20:27:18 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 20:27:18 - allora - INFO - ✅ Centralized configuration applied
2025-07-13 20:27:18 - engineio.server - INFO - Server initialized for threading.
2025-07-13 20:27:18 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 20:27:18 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 20:27:18 - allora - INFO - ✅ CORS configured with origins: ['http://localhost:3000', 'http://localhost:3001']
2025-07-13 20:27:18 - allora - INFO - ✅ JWT configuration applied from centralized config
2025-07-13 20:27:18 - allora - INFO - ✅ SECRET_KEY configuration applied from centralized config
2025-07-13 20:27:18 - allora - INFO - ✅ Database configuration applied from centralized config
2025-07-13 20:27:18 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 20:27:18 - allora - INFO - 🔧 Pool size: 10
2025-07-13 20:27:18 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 20:27:18 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 20:27:18 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 20:27:18 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 20:27:18 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.019s]
2025-07-13 20:27:18 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 20:27:18 - allora - INFO - Search API blueprint registered successfully
2025-07-13 20:27:18 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 20:27:19 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 20:27:19 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 20:27:19 - order_fulfillment.fulfillment_rules - INFO - Loaded 11 default fulfillment rules
2025-07-13 20:27:19 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 20:27:19 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 20:27:19 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 20:27:19 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 20:27:19 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 20:27:19 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 20:27:19 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 20:27:19 - allora - INFO - Recommendation system initialized successfully
2025-07-13 20:27:19 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 20:27:19 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 20:27:19 - tracking_system - INFO - Real-time tracking system started
2025-07-13 20:27:19 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 20:27:19 - notification_service - INFO - Notification delivery service started
2025-07-13 20:27:19 - allora - INFO - Notification service initialized successfully
2025-07-13 20:27:19 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 20:27:19 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.024s]
2025-07-13 20:27:19 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 20:30:49 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 20:30:49 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 20:30:49 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 20:30:49 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 20:30:49 - startup - INFO - ============================================================
2025-07-13 20:30:49 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 20:30:49 - startup - INFO - ============================================================
2025-07-13 20:30:49 - startup - INFO - 📅 Startup Time: 2025-07-13T20:30:49.618931
2025-07-13 20:30:49 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 20:30:49 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 20:30:49 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 20:30:49 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 20:30:49 - startup - INFO - ============================================================
2025-07-13 20:30:49 - allora - INFO - 🔧 Using configuration: ProductionConfig
2025-07-13 20:30:49 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 20:30:49 - allora - INFO - ✅ Centralized configuration applied
2025-07-13 20:30:49 - engineio.server - INFO - Server initialized for threading.
2025-07-13 20:30:49 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 20:30:49 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 20:30:49 - allora - INFO - ✅ CORS configured with origins: ['http://localhost:3000', 'http://localhost:3001']
2025-07-13 20:30:49 - allora - INFO - ✅ JWT configuration applied from centralized config
2025-07-13 20:30:49 - allora - INFO - ✅ SECRET_KEY configuration applied from centralized config
2025-07-13 20:30:49 - allora - INFO - ✅ Database configuration applied from centralized config
2025-07-13 20:30:49 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 20:30:49 - allora - INFO - 🔧 Pool size: 10
2025-07-13 20:30:49 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 20:30:49 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 20:30:49 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 20:30:49 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 20:30:50 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.003s]
2025-07-13 20:30:50 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 20:30:50 - allora - INFO - Search API blueprint registered successfully
2025-07-13 20:30:50 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 20:30:50 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 20:30:50 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 20:30:50 - order_fulfillment.fulfillment_rules - INFO - Loaded 11 default fulfillment rules
2025-07-13 20:30:50 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 20:30:50 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 20:30:50 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 20:30:50 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 20:30:50 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 20:30:50 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 20:30:50 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 20:30:50 - allora - INFO - Recommendation system initialized successfully
2025-07-13 20:30:50 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 20:30:50 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 20:30:50 - tracking_system - INFO - Real-time tracking system started
2025-07-13 20:30:50 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 20:30:50 - notification_service - INFO - Notification delivery service started
2025-07-13 20:30:50 - allora - INFO - Notification service initialized successfully
2025-07-13 20:30:50 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 20:33:51 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 20:33:51 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 20:33:51 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 20:33:51 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 20:33:51 - startup - INFO - ============================================================
2025-07-13 20:33:51 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 20:33:51 - startup - INFO - ============================================================
2025-07-13 20:33:51 - startup - INFO - 📅 Startup Time: 2025-07-13T20:33:51.349347
2025-07-13 20:33:51 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 20:33:51 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 20:33:51 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 20:33:51 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 20:33:51 - startup - INFO - ============================================================
2025-07-13 20:33:51 - allora - INFO - 🔧 Using configuration: ProductionConfig
2025-07-13 20:33:51 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 20:33:51 - allora - INFO - ✅ Centralized configuration applied
2025-07-13 20:33:51 - engineio.server - INFO - Server initialized for threading.
2025-07-13 20:33:51 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 20:33:51 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 20:33:51 - allora - INFO - ✅ CORS configured with origins: ['http://localhost:3000', 'http://localhost:3001']
2025-07-13 20:33:51 - allora - INFO - ✅ JWT configuration applied from centralized config
2025-07-13 20:33:51 - allora - INFO - ✅ SECRET_KEY configuration applied from centralized config
2025-07-13 20:33:51 - allora - INFO - ✅ Database configuration applied from centralized config
2025-07-13 20:33:51 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 20:33:51 - allora - INFO - 🔧 Pool size: 10
2025-07-13 20:33:51 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 20:33:51 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 20:33:51 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 20:33:51 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 20:33:51 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.004s]
2025-07-13 20:33:51 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 20:33:51 - allora - INFO - Search API blueprint registered successfully
2025-07-13 20:33:51 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 20:33:52 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 20:33:52 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 20:33:52 - order_fulfillment.fulfillment_rules - INFO - Loaded 11 default fulfillment rules
2025-07-13 20:33:52 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 20:33:52 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 20:33:52 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 20:33:52 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 20:33:52 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 20:33:52 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 20:33:52 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 20:33:52 - allora - INFO - Recommendation system initialized successfully
2025-07-13 20:33:52 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 20:33:52 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 20:33:52 - tracking_system - INFO - Real-time tracking system started
2025-07-13 20:33:52 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 20:33:52 - notification_service - INFO - Notification delivery service started
2025-07-13 20:33:52 - allora - INFO - Notification service initialized successfully
2025-07-13 20:33:52 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 20:42:45 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 20:42:45 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 20:42:45 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 20:42:45 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 20:42:45 - startup - INFO - ============================================================
2025-07-13 20:42:45 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 20:42:45 - startup - INFO - ============================================================
2025-07-13 20:42:45 - startup - INFO - 📅 Startup Time: 2025-07-13T20:42:45.900996
2025-07-13 20:42:45 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 20:42:45 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 20:42:45 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 20:42:45 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 20:42:45 - startup - INFO - ============================================================
2025-07-13 20:42:45 - allora - INFO - 🔧 Using configuration: ProductionConfig
2025-07-13 20:42:45 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 20:42:45 - allora - INFO - ✅ Centralized configuration applied
2025-07-13 20:42:45 - engineio.server - INFO - Server initialized for threading.
2025-07-13 20:42:45 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 20:42:45 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 20:42:45 - allora - INFO - ✅ CORS configured with origins: ['http://localhost:3000', 'http://localhost:3001']
2025-07-13 20:42:45 - allora - INFO - ✅ JWT configuration applied from centralized config
2025-07-13 20:42:45 - allora - INFO - ✅ SECRET_KEY configuration applied from centralized config
2025-07-13 20:42:45 - allora - INFO - ✅ Database configuration applied from centralized config
2025-07-13 20:42:46 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 20:42:46 - allora - INFO - 🔧 Pool size: 10
2025-07-13 20:42:46 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 20:42:46 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 20:42:46 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 20:42:46 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 20:42:46 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.036s]
2025-07-13 20:42:46 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 20:42:46 - allora - INFO - Search API blueprint registered successfully
2025-07-13 20:42:46 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 20:42:46 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 20:42:46 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 20:42:46 - order_fulfillment.fulfillment_rules - INFO - Loaded 11 default fulfillment rules
2025-07-13 20:42:46 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 20:42:46 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 20:42:46 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 20:42:46 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 20:42:46 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 20:42:46 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 20:42:46 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 20:42:46 - allora - INFO - Recommendation system initialized successfully
2025-07-13 20:42:46 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 20:42:46 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 20:42:46 - tracking_system - INFO - Real-time tracking system started
2025-07-13 20:42:46 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 20:42:46 - notification_service - INFO - Notification delivery service started
2025-07-13 20:42:46 - allora - INFO - Notification service initialized successfully
2025-07-13 20:42:46 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 20:42:47 - allora - WARNING - 404 error for path: /api/dashboard/metrics
2025-07-13 20:42:47 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 20:42:47] "[33mGET /api/dashboard/metrics HTTP/1.1[0m" 404 -
2025-07-13 20:42:47 - allora - WARNING - 404 error for path: /api/dashboard/shipments
2025-07-13 20:42:47 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 20:42:47] "[33mGET /api/dashboard/shipments HTTP/1.1[0m" 404 -
2025-07-13 20:42:47 - webhook_handlers - ERROR - Error parsing timestamp None: strptime() argument 1 must be str, not None
2025-07-13 20:42:47 - webhook_handlers - WARNING - No tracking info found for TEST123456
2025-07-13 20:42:47 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 20:42:47] "[31m[1mPOST /api/webhooks/test HTTP/1.1[0m" 400 -
2025-07-13 20:45:08 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 20:45:08 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 20:45:08 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 20:45:08 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 20:45:08 - startup - INFO - ============================================================
2025-07-13 20:45:08 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 20:45:08 - startup - INFO - ============================================================
2025-07-13 20:45:08 - startup - INFO - 📅 Startup Time: 2025-07-13T20:45:08.515446
2025-07-13 20:45:08 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 20:45:08 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 20:45:08 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 20:45:08 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 20:45:08 - startup - INFO - ============================================================
2025-07-13 20:45:08 - allora - INFO - 🔧 Using configuration: ProductionConfig
2025-07-13 20:45:08 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 20:45:08 - allora - INFO - ✅ Centralized configuration applied
2025-07-13 20:45:08 - engineio.server - INFO - Server initialized for threading.
2025-07-13 20:45:08 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 20:45:08 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 20:45:08 - allora - INFO - ✅ CORS configured with origins: ['http://localhost:3000', 'http://localhost:3001']
2025-07-13 20:45:08 - allora - INFO - ✅ JWT configuration applied from centralized config
2025-07-13 20:45:08 - allora - INFO - ✅ SECRET_KEY configuration applied from centralized config
2025-07-13 20:45:08 - allora - INFO - ✅ Database configuration applied from centralized config
2025-07-13 20:45:08 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 20:45:08 - allora - INFO - 🔧 Pool size: 10
2025-07-13 20:45:08 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 20:45:08 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 20:45:08 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 20:45:08 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 20:45:09 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.012s]
2025-07-13 20:45:09 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 20:45:09 - allora - INFO - Search API blueprint registered successfully
2025-07-13 20:45:09 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 20:45:09 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 20:45:09 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 20:45:09 - order_fulfillment.fulfillment_rules - INFO - Loaded 11 default fulfillment rules
2025-07-13 20:45:09 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 20:45:09 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 20:45:09 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 20:45:09 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 20:45:09 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 20:45:09 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 20:45:09 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 20:45:09 - allora - INFO - Recommendation system initialized successfully
2025-07-13 20:45:09 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 20:45:09 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 20:45:09 - tracking_system - INFO - Real-time tracking system started
2025-07-13 20:45:09 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 20:45:09 - notification_service - INFO - Notification delivery service started
2025-07-13 20:45:09 - allora - INFO - Notification service initialized successfully
2025-07-13 20:45:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 20:45:10 - tracking_dashboard - ERROR - Error getting basic metrics: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 20:45:10 - tracking_dashboard - ERROR - Error getting carrier performance: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 20:45:10 - tracking_dashboard - ERROR - Error getting daily volume: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 20:45:10 - tracking_dashboard - ERROR - Error getting status distribution: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 20:45:10 - tracking_dashboard - ERROR - Error getting recent exceptions: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 20:45:10 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 20:45:10] "GET /admin/tracking/api/metrics HTTP/1.1" 200 -
2025-07-13 20:45:10 - tracking_dashboard - ERROR - Error getting shipment list: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 20:45:10 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 20:45:10] "GET /admin/tracking/api/shipments HTTP/1.1" 200 -
2025-07-13 20:45:10 - webhook_handlers - ERROR - Error parsing timestamp None: strptime() argument 1 must be str, not None
2025-07-13 20:45:10 - webhook_handlers - WARNING - No tracking info found for TEST123456
2025-07-13 20:45:10 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 20:45:10] "[31m[1mPOST /api/webhooks/test HTTP/1.1[0m" 400 -
2025-07-13 20:45:10 - webhook_handlers - WARNING - Invalid Blue Dart webhook signature
2025-07-13 20:45:10 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 20:45:10] "[31m[1mPOST /api/webhooks/blue-dart HTTP/1.1[0m" 401 -
2025-07-13 21:05:08 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 21:05:08 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 21:05:08 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 21:05:08 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 21:05:08 - startup - INFO - ============================================================
2025-07-13 21:05:08 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 21:05:08 - startup - INFO - ============================================================
2025-07-13 21:05:08 - startup - INFO - 📅 Startup Time: 2025-07-13T21:05:08.657968
2025-07-13 21:05:08 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 21:05:08 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 21:05:08 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 21:05:08 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 21:05:08 - startup - INFO - ============================================================
2025-07-13 21:05:08 - allora - INFO - 🔧 Using configuration: ProductionConfig
2025-07-13 21:05:08 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 21:05:08 - allora - INFO - ✅ Centralized configuration applied
2025-07-13 21:05:08 - engineio.server - INFO - Server initialized for threading.
2025-07-13 21:05:08 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 21:05:08 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 21:05:08 - allora - INFO - ✅ CORS configured with origins: ['http://localhost:3000', 'http://localhost:3001']
2025-07-13 21:05:08 - allora - INFO - ✅ JWT configuration applied from centralized config
2025-07-13 21:05:08 - allora - INFO - ✅ SECRET_KEY configuration applied from centralized config
2025-07-13 21:05:08 - allora - INFO - ✅ Database configuration applied from centralized config
2025-07-13 21:05:08 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 21:05:08 - allora - INFO - 🔧 Pool size: 10
2025-07-13 21:05:08 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 21:05:08 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 21:05:08 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 21:05:08 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 21:05:09 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.022s]
2025-07-13 21:05:09 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 21:05:09 - allora - INFO - Search API blueprint registered successfully
2025-07-13 21:05:09 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 21:05:09 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 21:05:09 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 21:05:09 - order_fulfillment.fulfillment_rules - INFO - Loaded 11 default fulfillment rules
2025-07-13 21:05:09 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 21:05:09 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 21:05:09 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 21:05:09 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 21:05:09 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 21:05:09 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 21:05:09 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 21:05:09 - allora - INFO - Recommendation system initialized successfully
2025-07-13 21:05:09 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 21:05:09 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 21:05:09 - tracking_system - INFO - Real-time tracking system started
2025-07-13 21:05:09 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 21:05:09 - notification_service - INFO - Notification delivery service started
2025-07-13 21:05:09 - allora - INFO - Notification service initialized successfully
2025-07-13 21:05:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:05:10 - webhook_handlers - WARNING - Invalid Blue Dart webhook signature
2025-07-13 21:05:10 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 21:05:10] "[31m[1mPOST /api/webhooks/blue-dart HTTP/1.1[0m" 401 -
2025-07-13 21:05:10 - webhook_handlers - WARNING - Invalid Delhivery webhook signature
2025-07-13 21:05:10 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 21:05:10] "[31m[1mPOST /api/webhooks/delhivery HTTP/1.1[0m" 401 -
2025-07-13 21:05:10 - webhook_handlers - WARNING - Invalid FedEx webhook signature
2025-07-13 21:05:10 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 21:05:10] "[31m[1mPOST /api/webhooks/fedex HTTP/1.1[0m" 401 -
2025-07-13 21:05:10 - webhook_handlers - WARNING - No tracking info found for TEST123456
2025-07-13 21:05:10 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 21:05:10] "[31m[1mPOST /api/webhooks/test HTTP/1.1[0m" 400 -
2025-07-13 21:05:10 - fulfillment_api - INFO - Received webhook from blue_dart: {'tracking_number': 'BD123456789', 'status': 'delivered', 'location': 'Mumbai', 'timestamp': '2025-07-13 20:00:00'}
2025-07-13 21:05:10 - fulfillment_api - ERROR - No tracking number in webhook from blue_dart
2025-07-13 21:05:10 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 21:05:10] "[31m[1mPOST /api/fulfillment/webhooks/blue_dart/tracking HTTP/1.1[0m" 400 -
2025-07-13 21:05:10 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 21:05:10] "[35m[1mPOST /api/webhooks/inventory/1 HTTP/1.1[0m" 500 -
2025-07-13 21:06:20 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 21:06:20 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 21:06:20 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 21:06:20 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 21:06:20 - startup - INFO - ============================================================
2025-07-13 21:06:20 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 21:06:20 - startup - INFO - ============================================================
2025-07-13 21:06:20 - startup - INFO - 📅 Startup Time: 2025-07-13T21:06:20.554713
2025-07-13 21:06:20 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 21:06:20 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 21:06:20 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 21:06:20 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 21:06:20 - startup - INFO - ============================================================
2025-07-13 21:06:20 - allora - INFO - 🔧 Using configuration: ProductionConfig
2025-07-13 21:06:20 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 21:06:20 - allora - INFO - ✅ Centralized configuration applied
2025-07-13 21:06:20 - engineio.server - INFO - Server initialized for threading.
2025-07-13 21:06:20 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 21:06:20 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 21:06:20 - allora - INFO - ✅ CORS configured with origins: ['http://localhost:3000', 'http://localhost:3001']
2025-07-13 21:06:20 - allora - INFO - ✅ JWT configuration applied from centralized config
2025-07-13 21:06:20 - allora - INFO - ✅ SECRET_KEY configuration applied from centralized config
2025-07-13 21:06:20 - allora - INFO - ✅ Database configuration applied from centralized config
2025-07-13 21:06:20 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 21:06:20 - allora - INFO - 🔧 Pool size: 10
2025-07-13 21:06:20 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 21:06:20 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 21:06:20 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 21:06:20 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 21:06:21 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.033s]
2025-07-13 21:06:21 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 21:06:21 - allora - INFO - Search API blueprint registered successfully
2025-07-13 21:06:21 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 21:06:21 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 21:06:21 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 21:06:21 - order_fulfillment.fulfillment_rules - INFO - Loaded 11 default fulfillment rules
2025-07-13 21:06:21 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 21:06:21 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 21:06:21 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 21:06:21 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 21:06:21 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 21:06:21 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 21:06:21 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 21:06:21 - allora - INFO - Recommendation system initialized successfully
2025-07-13 21:06:21 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 21:06:21 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 21:06:21 - tracking_system - INFO - Real-time tracking system started
2025-07-13 21:06:21 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 21:06:21 - notification_service - INFO - Notification delivery service started
2025-07-13 21:06:21 - allora - INFO - Notification service initialized successfully
2025-07-13 21:06:21 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:07:50 - webhook_handlers - ERROR - Error parsing timestamp None: strptime() argument 1 must be str, not None
2025-07-13 21:07:50 - webhook_handlers - WARNING - No tracking info found for TEST123
2025-07-13 21:07:50 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 21:07:50] "[31m[1mPOST /api/webhooks/test HTTP/1.1[0m" 400 -
2025-07-13 21:16:39 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 21:16:39 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 21:16:39 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 21:16:39 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 21:16:39 - startup - INFO - ============================================================
2025-07-13 21:16:39 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 21:16:39 - startup - INFO - ============================================================
2025-07-13 21:16:39 - startup - INFO - 📅 Startup Time: 2025-07-13T21:16:39.050232
2025-07-13 21:16:39 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 21:16:39 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 21:16:39 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 21:16:39 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 21:16:39 - startup - INFO - ============================================================
2025-07-13 21:16:39 - allora - INFO - 🔧 Using configuration: ProductionConfig
2025-07-13 21:16:39 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 21:16:39 - allora - INFO - ✅ Centralized configuration applied
2025-07-13 21:16:39 - engineio.server - INFO - Server initialized for threading.
2025-07-13 21:16:39 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 21:16:39 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 21:16:39 - allora - INFO - ✅ CORS configured with origins: ['http://localhost:3000', 'http://localhost:3001']
2025-07-13 21:16:39 - allora - INFO - ✅ JWT configuration applied from centralized config
2025-07-13 21:16:39 - allora - INFO - ✅ SECRET_KEY configuration applied from centralized config
2025-07-13 21:16:39 - allora - INFO - ✅ Database configuration applied from centralized config
2025-07-13 21:16:39 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 21:16:39 - allora - INFO - 🔧 Pool size: 10
2025-07-13 21:16:39 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 21:16:39 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 21:16:39 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 21:16:39 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 21:16:39 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.030s]
2025-07-13 21:16:39 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 21:16:39 - allora - INFO - Search API blueprint registered successfully
2025-07-13 21:16:39 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 21:16:40 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 21:16:40 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 21:16:40 - order_fulfillment.fulfillment_rules - INFO - Loaded 11 default fulfillment rules
2025-07-13 21:16:40 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 21:16:40 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 21:16:40 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 21:16:40 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 21:16:40 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 21:16:40 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 21:16:40 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 21:16:40 - allora - INFO - Recommendation system initialized successfully
2025-07-13 21:16:40 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 21:16:40 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 21:16:40 - tracking_system - INFO - Real-time tracking system started
2025-07-13 21:16:40 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 21:16:40 - notification_service - INFO - Notification delivery service started
2025-07-13 21:16:40 - allora - INFO - Notification service initialized successfully
2025-07-13 21:16:40 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:21:34 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 21:21:34 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 21:21:34 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 21:21:34 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 21:21:34 - startup - INFO - ============================================================
2025-07-13 21:21:34 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 21:21:34 - startup - INFO - ============================================================
2025-07-13 21:21:34 - startup - INFO - 📅 Startup Time: 2025-07-13T21:21:34.727985
2025-07-13 21:21:34 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 21:21:34 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 21:21:34 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 21:21:34 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 21:21:34 - startup - INFO - ============================================================
2025-07-13 21:21:34 - allora - INFO - 🔧 Using configuration: ProductionConfig
2025-07-13 21:21:34 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 21:21:34 - allora - INFO - ✅ Centralized configuration applied
2025-07-13 21:21:34 - engineio.server - INFO - Server initialized for threading.
2025-07-13 21:21:34 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 21:21:34 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 21:21:34 - allora - INFO - ✅ CORS configured with origins: ['http://localhost:3000', 'http://localhost:3001']
2025-07-13 21:21:34 - allora - INFO - ✅ JWT configuration applied from centralized config
2025-07-13 21:21:34 - allora - INFO - ✅ SECRET_KEY configuration applied from centralized config
2025-07-13 21:21:34 - allora - INFO - ✅ Database configuration applied from centralized config
2025-07-13 21:21:34 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 21:21:34 - allora - INFO - 🔧 Pool size: 10
2025-07-13 21:21:34 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 21:21:34 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 21:21:34 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 21:21:34 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 21:21:35 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-13 21:21:35 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 21:21:35 - allora - INFO - Search API blueprint registered successfully
2025-07-13 21:21:35 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 21:21:35 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 21:21:35 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 21:21:35 - order_fulfillment.fulfillment_rules - INFO - Loaded 11 default fulfillment rules
2025-07-13 21:21:35 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 21:21:35 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 21:21:35 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 21:21:35 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 21:21:35 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 21:21:35 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 21:21:35 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 21:21:35 - allora - INFO - Recommendation system initialized successfully
2025-07-13 21:21:35 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 21:21:35 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 21:21:35 - tracking_system - INFO - Real-time tracking system started
2025-07-13 21:21:35 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 21:21:35 - notification_service - INFO - Notification delivery service started
2025-07-13 21:21:35 - allora - INFO - Notification service initialized successfully
2025-07-13 21:21:35 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:30:02 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 21:30:02 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 21:30:02 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 21:30:02 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 21:30:02 - startup - INFO - ============================================================
2025-07-13 21:30:02 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 21:30:02 - startup - INFO - ============================================================
2025-07-13 21:30:02 - startup - INFO - 📅 Startup Time: 2025-07-13T21:30:02.466849
2025-07-13 21:30:02 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 21:30:02 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 21:30:02 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 21:30:02 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 21:30:02 - startup - INFO - ============================================================
2025-07-13 21:30:02 - allora - INFO - 🔧 Using configuration: ProductionConfig
2025-07-13 21:30:02 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 21:30:02 - allora - INFO - ✅ Centralized configuration applied
2025-07-13 21:30:02 - engineio.server - INFO - Server initialized for threading.
2025-07-13 21:30:02 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 21:30:02 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 21:30:02 - allora - INFO - ✅ CORS configured with origins: ['http://localhost:3000', 'http://localhost:3001']
2025-07-13 21:30:02 - allora - INFO - ✅ JWT configuration applied from centralized config
2025-07-13 21:30:02 - allora - INFO - ✅ SECRET_KEY configuration applied from centralized config
2025-07-13 21:30:02 - allora - INFO - ✅ Database configuration applied from centralized config
2025-07-13 21:30:02 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 21:30:02 - allora - INFO - 🔧 Pool size: 10
2025-07-13 21:30:02 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 21:30:02 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 21:30:02 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-13 21:30:02 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 21:30:02 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.023s]
2025-07-13 21:30:02 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 21:30:02 - allora - INFO - Search API blueprint registered successfully
2025-07-13 21:30:02 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 21:30:03 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 21:30:03 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 21:30:03 - order_fulfillment.fulfillment_rules - INFO - Loaded 11 default fulfillment rules
2025-07-13 21:30:03 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 21:30:03 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 21:30:03 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 21:30:03 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 21:30:03 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 21:30:03 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 21:30:03 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 21:30:03 - allora - INFO - Recommendation system initialized successfully
2025-07-13 21:30:03 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 21:30:03 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 21:30:03 - tracking_system - INFO - Real-time tracking system started
2025-07-13 21:30:03 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 21:30:03 - notification_service - INFO - Notification delivery service started
2025-07-13 21:30:03 - allora - INFO - Notification service initialized successfully
2025-07-13 21:30:03 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:30:03 - allora - INFO - Test log message from integration test
2025-07-13 21:30:03 - allora - WARNING - Test warning message
2025-07-13 21:30:03 - allora - ERROR - Test error message
2025-07-13 21:30:03 - allora - INFO - PERFORMANCE: test_function completed in 0.024s
2025-07-13 21:30:03 - __main__ - ERROR - PERFORMANCE: failing_function failed after 0.000s - Test exception
2025-07-13 21:37:02 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 21:37:02 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 21:37:02 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 21:37:02 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 21:37:02 - startup - INFO - ============================================================
2025-07-13 21:37:02 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 21:37:02 - startup - INFO - ============================================================
2025-07-13 21:37:02 - startup - INFO - 📅 Startup Time: 2025-07-13T21:37:02.665171
2025-07-13 21:37:02 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 21:37:02 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 21:37:02 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 21:37:02 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 21:37:02 - startup - INFO - ============================================================
2025-07-13 21:37:02 - allora - INFO - 🔧 Using configuration: ProductionConfig
2025-07-13 21:37:02 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 21:37:02 - allora - INFO - ✅ Centralized configuration applied
2025-07-13 21:37:02 - engineio.server - INFO - Server initialized for threading.
2025-07-13 21:37:02 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-13 21:37:02 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-13 21:37:02 - allora - INFO - ✅ CORS configured with origins: ['http://localhost:3000', 'http://localhost:3001']
2025-07-13 21:37:02 - allora - INFO - ✅ JWT configuration applied from centralized config
2025-07-13 21:37:02 - allora - INFO - ✅ SECRET_KEY configuration applied from centralized config
2025-07-13 21:37:02 - allora - INFO - ✅ Database configuration applied from centralized config
2025-07-13 21:37:02 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-13 21:37:02 - allora - INFO - 🔧 Pool size: 10
2025-07-13 21:37:02 - allora - INFO - 🔧 Max overflow: 20
2025-07-13 21:37:02 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-13 21:37:02 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-13 21:37:03 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.025s]
2025-07-13 21:37:03 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 21:37:03 - allora - INFO - Search API blueprint registered successfully
2025-07-13 21:37:03 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 21:37:03 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 21:37:03 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 21:37:03 - order_fulfillment.fulfillment_rules - INFO - Loaded 11 default fulfillment rules
2025-07-13 21:37:03 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 21:37:03 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 21:37:03 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 21:37:03 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 21:37:03 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 21:37:03 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 21:37:03 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 21:37:03 - allora - INFO - Recommendation system initialized successfully
2025-07-13 21:37:03 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 21:37:03 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 21:37:03 - tracking_system - INFO - Real-time tracking system started
2025-07-13 21:37:03 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 21:37:03 - notification_service - INFO - Notification delivery service started
2025-07-13 21:37:03 - allora - INFO - Notification service initialized successfully
2025-07-13 21:37:03 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:37:04 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 21:37:04] "GET /api/cache/test HTTP/1.1" 200 -
2025-07-13 21:37:04 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 21:37:04] "GET /api/session/test HTTP/1.1" 200 -
2025-07-13 21:37:04 - werkzeug - INFO - 127.0.0.1 - - [13/Jul/2025 21:37:04] "GET /api/health HTTP/1.1" 200 -
