#!/usr/bin/env python3
"""
Test script for inventory scheduler functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_inventory_scheduler_imports():
    """Test if inventory scheduler can be imported without circular import issues"""
    print("🧪 TESTING INVENTORY SCHEDULER IMPORTS")
    print("=" * 60)
    
    try:
        from inventory_scheduler import InventorySyncScheduler, scheduler
        print("✅ inventory_scheduler.py imports successful")
        
        # Test scheduler instance
        print(f"✅ Scheduler instance created: {type(scheduler)}")
        print(f"✅ Scheduler running status: {scheduler.running}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_scheduler_methods():
    """Test scheduler methods availability"""
    print("\n🧪 TESTING SCHEDULER METHODS")
    print("=" * 60)
    
    try:
        from inventory_scheduler import scheduler
        
        # Test method availability
        methods = [
            'start', 'stop', 'process_sync_queue', 'check_scheduled_syncs',
            'retry_failed_syncs', 'cleanup_old_logs', 'detect_and_resolve_conflicts',
            'health_check'
        ]
        
        for method in methods:
            if hasattr(scheduler, method):
                print(f"✅ Method available: {method}")
            else:
                print(f"❌ Method missing: {method}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing methods: {e}")
        return False

def test_scheduler_initialization():
    """Test scheduler initialization without starting"""
    print("\n🧪 TESTING SCHEDULER INITIALIZATION")
    print("=" * 60)
    
    try:
        from inventory_scheduler import InventorySyncScheduler
        
        # Create new instance
        test_scheduler = InventorySyncScheduler()
        print("✅ Scheduler instance created successfully")
        
        # Test properties
        print(f"✅ Running status: {test_scheduler.running}")
        print(f"✅ Max concurrent syncs: {test_scheduler.max_concurrent_syncs}")
        print(f"✅ Retry delays: {test_scheduler.retry_delays}")
        
        # Test lazy loading methods
        try:
            app, db = test_scheduler._get_app_components()
            print("✅ App components loaded successfully")
        except Exception as e:
            print(f"⚠️  App components loading: {e}")
        
        try:
            models = test_scheduler._get_models()
            print(f"✅ Models loaded: {type(models)}")
            if models:
                print(f"   Available models: {list(models.keys())}")
        except Exception as e:
            print(f"⚠️  Models loading: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in initialization test: {e}")
        return False

def test_scheduler_integration():
    """Test scheduler integration with app"""
    print("\n🧪 TESTING SCHEDULER INTEGRATION")
    print("=" * 60)
    
    try:
        # Test scheduler_init integration
        from scheduler_init import register_inventory_scheduler, get_scheduler_manager
        
        print("✅ Scheduler init imports successful")
        
        # Test registration
        register_inventory_scheduler()
        print("✅ Scheduler registration completed")
        
        # Test manager
        manager = get_scheduler_manager()
        status = manager.get_status()
        print(f"✅ Scheduler manager status: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in integration test: {e}")
        return False

def main():
    """Run all inventory scheduler tests"""
    print("🚀 STARTING INVENTORY SCHEDULER TESTS")
    print("=" * 70)
    
    tests = [
        test_inventory_scheduler_imports,
        test_scheduler_methods,
        test_scheduler_initialization,
        test_scheduler_integration
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 INVENTORY SCHEDULER TEST RESULTS SUMMARY")
    print("=" * 70)
    
    test_names = [
        "Import Tests",
        "Method Tests", 
        "Initialization Tests",
        "Integration Tests"
    ]
    
    passed = sum(results)
    total = len(results)
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{name:<25} {status}")
    
    print(f"\n🎯 OVERALL RESULT: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Inventory scheduler is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the details above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
