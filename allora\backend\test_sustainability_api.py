#!/usr/bin/env python3
"""
Sustainability API Test Suite
=============================

Comprehensive testing for the sustainability API functionality.
Tests all components, metrics calculation, and integration.

Author: Allora Development Team
Date: 2025-07-13
"""

import sys
import os
import time
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_sustainability_api_structure():
    """Test the sustainability API module structure"""
    print("🔍 Testing Sustainability API Structure")
    print("=" * 50)
    
    try:
        # Import sustainability API module
        import sustainability_api
        
        print("✅ Sustainability API module imported successfully")
        
        # Test core components
        required_components = [
            'sustainability_bp', 'get_models', 'admin_required'
        ]
        
        for component in required_components:
            if hasattr(sustainability_api, component):
                print(f"   ✅ {component}: Found")
            else:
                print(f"   ❌ {component}: Missing")
        
        # Test calculation functions
        calculation_functions = [
            'calculate_co2_savings', 'calculate_trees_planted_equivalent', 
            'calculate_plastic_saved'
        ]
        
        for func_name in calculation_functions:
            if hasattr(sustainability_api, func_name):
                print(f"   ✅ {func_name}: Found")
            else:
                print(f"   ❌ {func_name}: Missing")
        
        # Test registration function
        if hasattr(sustainability_api, 'register_sustainability_api'):
            print(f"   ✅ register_sustainability_api: Found")
        else:
            print(f"   ❌ register_sustainability_api: Missing")
        
        return True
        
    except ImportError as e:
        print(f"❌ Sustainability API import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Sustainability API structure test error: {e}")
        return False

def test_calculation_functions():
    """Test sustainability calculation functions"""
    print("\n🧮 Testing Calculation Functions")
    print("=" * 40)
    
    try:
        from sustainability_api import (
            calculate_co2_savings, 
            calculate_trees_planted_equivalent, 
            calculate_plastic_saved
        )
        
        # Test data
        test_orders_data = [
            {
                'items': [
                    {
                        'sustainability_score': 80,
                        'quantity': 2,
                        'recyclable': True,
                        'organic': False
                    },
                    {
                        'sustainability_score': 90,
                        'quantity': 1,
                        'recyclable': False,
                        'organic': True
                    }
                ]
            },
            {
                'items': [
                    {
                        'sustainability_score': 70,
                        'quantity': 3,
                        'recyclable': True,
                        'organic': True
                    }
                ]
            }
        ]
        
        # Test CO2 savings calculation
        co2_saved = calculate_co2_savings(test_orders_data)
        print(f"✅ CO2 savings calculation: {co2_saved} kg")
        
        # Test trees planted equivalent
        trees_equivalent = calculate_trees_planted_equivalent(co2_saved)
        print(f"✅ Trees planted equivalent: {trees_equivalent} trees")
        
        # Test plastic saved calculation
        plastic_saved = calculate_plastic_saved(test_orders_data)
        print(f"✅ Plastic saved calculation: {plastic_saved} kg")
        
        # Validate calculations
        print("   📊 Calculation Validation:")
        print(f"      • CO2 saved > 0: {co2_saved > 0}")
        print(f"      • Trees equivalent > 0: {trees_equivalent > 0}")
        print(f"      • Plastic saved > 0: {plastic_saved > 0}")
        print(f"      • Trees = CO2/22: {abs(trees_equivalent - co2_saved/22) < 0.1}")
        
        return True
        
    except Exception as e:
        print(f"❌ Calculation functions test error: {e}")
        return False

def test_blueprint_structure():
    """Test Flask blueprint structure"""
    print("\n🌐 Testing Blueprint Structure")
    print("=" * 35)
    
    try:
        from sustainability_api import sustainability_bp
        
        # Test blueprint properties
        print("✅ Sustainability blueprint imported")
        print(f"   ✅ Blueprint name: {sustainability_bp.name}")
        print(f"   ✅ URL prefix: {sustainability_bp.url_prefix}")
        
        # Test blueprint routes (check deferred functions)
        route_count = len(sustainability_bp.deferred_functions)
        print(f"   ✅ Blueprint routes: {route_count} registered")
        
        # Expected routes
        expected_routes = [
            '/metrics', '/green-heroes', '/goals', 
            '/admin/goals', '/admin/impact-settings'
        ]
        
        print("   📋 Expected Routes:")
        for route in expected_routes:
            print(f"      • {sustainability_bp.url_prefix}{route}")
        
        return True
        
    except Exception as e:
        print(f"❌ Blueprint structure test error: {e}")
        return False

def test_app_integration():
    """Test sustainability API integration with Flask app"""
    print("\n🔗 Testing App Integration")
    print("=" * 30)
    
    try:
        # Import Flask app
        from app import app
        
        print("✅ Flask app imported successfully")
        
        # Check if sustainability blueprint is registered
        blueprints = [bp.name for bp in app.blueprints.values()]
        if 'sustainability' in blueprints:
            print("   ✅ Sustainability blueprint: Registered")
        else:
            print("   ❌ Sustainability blueprint: Not registered")
        
        # Check sustainability routes
        sustainability_routes = []
        for rule in app.url_map.iter_rules():
            if '/api/sustainability' in rule.rule:
                sustainability_routes.append(rule.rule)
        
        if sustainability_routes:
            print(f"   ✅ Sustainability routes: {len(sustainability_routes)} found")
            for route in sustainability_routes:
                print(f"      • {route}")
        else:
            print("   ❌ No sustainability routes found")
        
        return True
        
    except Exception as e:
        print(f"❌ App integration test error: {e}")
        return False

def test_api_endpoints():
    """Test sustainability API endpoints"""
    print("\n🌐 Testing API Endpoints")
    print("=" * 30)
    
    try:
        import requests
        
        base_url = "http://127.0.0.1:5000"
        
        # Test endpoints
        endpoints = [
            {
                'name': 'Sustainability Metrics',
                'url': f"{base_url}/api/sustainability/metrics",
                'method': 'GET',
                'description': 'Get sustainability impact metrics'
            },
            {
                'name': 'Green Heroes',
                'url': f"{base_url}/api/sustainability/green-heroes",
                'method': 'GET',
                'description': 'Get top sustainable products'
            },
            {
                'name': 'Sustainability Goals',
                'url': f"{base_url}/api/sustainability/goals",
                'method': 'GET',
                'description': 'Get sustainability goals and progress'
            }
        ]
        
        for endpoint in endpoints:
            print(f"🔍 Testing: {endpoint['name']}")
            print(f"   URL: {endpoint['url']}")
            print(f"   Method: {endpoint['method']}")
            
            try:
                if endpoint['method'] == 'GET':
                    response = requests.get(endpoint['url'], timeout=10)
                else:
                    response = requests.post(endpoint['url'], timeout=10)
                
                print(f"   ✅ Status Code: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"   ✅ Response Format: Valid JSON")
                        
                        if 'success' in data:
                            print(f"   ✅ Success: {data['success']}")
                        
                        if 'data' in data:
                            response_data = data['data']
                            if isinstance(response_data, dict):
                                print(f"   ✅ Data Keys: {list(response_data.keys())}")
                            elif isinstance(response_data, list):
                                print(f"   ✅ Data Items: {len(response_data)} items")
                        
                    except:
                        print(f"   ⚠️  Response: Non-JSON content")
                else:
                    print(f"   ⚠️  HTTP Status: {response.status_code}")
                    
            except requests.exceptions.ConnectionError:
                print(f"   ❌ Connection Error: Server not running")
            except requests.exceptions.Timeout:
                print(f"   ❌ Timeout Error: Request took too long")
            except Exception as e:
                print(f"   ❌ Error: {e}")
            
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ API endpoints test error: {e}")
        return False

def test_database_integration():
    """Test database model integration"""
    print("\n🗄️ Testing Database Integration")
    print("=" * 35)
    
    try:
        from sustainability_api import get_models
        
        # Test model retrieval
        db, User, Product, Order, OrderItem, AdminUser = get_models()
        
        if db:
            print("✅ Database connection: Available")
            print(f"   ✅ Database engine: {db.engine}")
        else:
            print("❌ Database connection: Not available")
        
        # Test model availability
        models = {
            'User': User,
            'Product': Product,
            'Order': Order,
            'OrderItem': OrderItem,
            'AdminUser': AdminUser
        }
        
        for model_name, model_class in models.items():
            if model_class:
                print(f"   ✅ {model_name} model: Available")
            else:
                print(f"   ❌ {model_name} model: Not available")
        
        return True
        
    except Exception as e:
        print(f"❌ Database integration test error: {e}")
        return False

def analyze_sustainability_system():
    """Analyze the sustainability system's purpose and functionality"""
    print("\n📋 Sustainability System Analysis")
    print("=" * 40)
    
    print("🎯 PRIMARY PURPOSE:")
    print("   The Sustainability API provides comprehensive environmental")
    print("   impact tracking and sustainability metrics for the Allora")
    print("   e-commerce platform, promoting eco-friendly shopping.")
    print()
    
    print("🔧 KEY FEATURES:")
    print("   1. Environmental Impact Tracking")
    print("      • CO2 savings calculation based on sustainability scores")
    print("      • Tree planting equivalent metrics")
    print("      • Plastic waste reduction tracking")
    print()
    
    print("   2. Green Heroes System")
    print("      • Top sustainable products identification")
    print("      • Eco badges and certifications")
    print("      • Impact scoring and ranking")
    print()
    
    print("   3. Sustainability Goals")
    print("      • Monthly environmental targets")
    print("      • Progress tracking and visualization")
    print("      • Admin-configurable goal management")
    print()
    
    print("   4. Admin Management")
    print("      • Sustainability goal creation and management")
    print("      • Impact calculation settings configuration")
    print("      • Real-time metrics monitoring")
    print()
    
    print("📊 BUSINESS VALUE:")
    print("   • Promotes eco-friendly shopping behavior")
    print("   • Provides transparency in environmental impact")
    print("   • Encourages sustainable product purchases")
    print("   • Supports corporate sustainability initiatives")
    print("   • Enhances brand reputation through green initiatives")
    print()

def run_all_tests():
    """Run all sustainability API tests"""
    print("🚀 Sustainability API Test Suite")
    print("=" * 40)
    print()
    
    tests = [
        test_sustainability_api_structure,
        test_calculation_functions,
        test_blueprint_structure,
        test_app_integration,
        test_database_integration
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    # Analyze sustainability system
    analyze_sustainability_system()
    
    # Test API endpoints (requires running server)
    print("⚠️  Note: API endpoint testing requires the server to be running")
    print("   Start the server with: python run_with_waitress.py")
    print()
    
    try:
        test_api_endpoints()
    except Exception as e:
        print(f"❌ API endpoint testing failed: {e}")
        print("   This is expected if the server is not running")
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    passed = sum(results)
    total = len(results)
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Sustainability API is properly integrated.")
    else:
        print("⚠️  Some tests failed. Please review the sustainability API.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
