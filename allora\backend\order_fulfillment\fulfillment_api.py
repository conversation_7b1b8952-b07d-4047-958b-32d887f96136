"""
Order Fulfillment API Endpoints
===============================

Comprehensive Flask API endpoints for order fulfillment operations including:
- Order fulfillment management and automation
- Rate calculation across multiple carriers
- Shipment creation and label generation
- Tracking and status updates
- Pickup scheduling and management
- Webhook handling for carrier updates
- Analytics and performance monitoring
- Admin dashboard integration
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from datetime import datetime, date, timedelta
import json
import logging
from typing import Dict, List, Optional, Any
from enum import Enum

# Import our fulfillment system components
from .carrier_integration import (
    CarrierFactory, RateCalculationEngine, ShipmentManager,
    CarrierAPIError, ShippingCarrier
)
from .order_fulfillment_engine import OrderFulfillmentEngine, FulfillmentStatus
from tracking_system import TrackingStatus
from notification_service import NotificationService
from .order_fulfillment_architecture import Address, Package, TrackingEventType

# Database models will be imported lazily to avoid circular imports
def get_models():
    """Get database models using lazy import"""
    try:
        from app import db, Shipment, ShippingCarrier as CarrierModel, TrackingEvent as TrackingEventModel
        return db, Shipment, CarrierModel, TrackingEventModel
    except ImportError:
        return None, None, None, None

def with_models(func):
    """Decorator to inject database models into function"""
    def wrapper(*args, **kwargs):
        # Get models and inject them into the function's globals
        db, Shipment, CarrierModel, TrackingEventModel = get_models()
        if not db:
            return jsonify({'error': 'Database models not available'}), 500

        # Inject models into function's global namespace
        func.__globals__['db'] = db
        func.__globals__['Shipment'] = Shipment
        func.__globals__['CarrierModel'] = CarrierModel
        func.__globals__['TrackingEventModel'] = TrackingEventModel

        return func(*args, **kwargs)
    return wrapper

logger = logging.getLogger(__name__)

# Create Blueprint
fulfillment_bp = Blueprint('fulfillment', __name__, url_prefix='/api/fulfillment')

# Initialize managers
rate_engine = RateCalculationEngine()
shipment_manager = ShipmentManager()

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

def create_address_from_dict(data: dict) -> Address:
    """Create Address object from dictionary"""
    return Address(
        name=data.get('name', ''),
        company=data.get('company', ''),
        address_line_1=data.get('address_line_1', ''),
        address_line_2=data.get('address_line_2', ''),
        city=data.get('city', ''),
        state=data.get('state', ''),
        postal_code=data.get('postal_code', ''),
        country=data.get('country', 'India'),
        phone=data.get('phone', ''),
        email=data.get('email', '')
    )

def create_package_from_dict(data: dict) -> Package:
    """Create Package object from dictionary"""
    return Package(
        weight=float(data.get('weight', 1.0)),
        length=float(data.get('length', 10.0)),
        width=float(data.get('width', 10.0)),
        height=float(data.get('height', 10.0)),
        declared_value=float(data.get('declared_value', 100.0)),
        description=data.get('description', 'E-commerce Product')
    )

def validate_required_fields(data: dict, required_fields: list) -> tuple:
    """Validate required fields in request data"""
    missing_fields = [field for field in required_fields if field not in data or not data[field]]
    if missing_fields:
        return False, f"Missing required fields: {', '.join(missing_fields)}"
    return True, None

# ============================================================================
# ORDER FULFILLMENT MANAGEMENT ENDPOINTS
# ============================================================================

@fulfillment_bp.route('/orders', methods=['POST'])
@jwt_required()
def create_fulfillment():
    """Create new order fulfillment request"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        # Validate required fields
        required_fields = ['order_id', 'shipping_address', 'items']
        is_valid, error_msg = validate_required_fields(data, required_fields)
        if not is_valid:
            return jsonify({'error': error_msg}), 400

        # Get fulfillment engine
        fulfillment_engine = OrderFulfillmentEngine.get_instance()

        # Create fulfillment request
        result = fulfillment_engine.process_order(
            order_id=data['order_id'],
            shipping_address=data['shipping_address'],
            items=data['items'],
            shipping_preferences=data.get('shipping_preferences', {}),
            user_id=user_id
        )

        if result.success:
            return jsonify({
                'success': True,
                'fulfillment_id': result.fulfillment_id,
                'status': result.status,
                'estimated_delivery': result.estimated_delivery.isoformat() if result.estimated_delivery else None,
                'tracking_number': result.tracking_number,
                'carrier': result.carrier,
                'shipping_cost': result.shipping_cost
            })
        else:
            return jsonify({
                'success': False,
                'error': result.error_message,
                'error_code': result.error_code
            }), 400

    except Exception as e:
        logger.error(f"Error creating fulfillment: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@fulfillment_bp.route('/orders/<int:order_id>', methods=['GET'])
@jwt_required()
def get_fulfillment(order_id):
    """Get order fulfillment details"""
    try:
        user_id = get_jwt_identity()

        # Get fulfillment engine
        fulfillment_engine = OrderFulfillmentEngine.get_instance()

        # Get fulfillment details
        fulfillment = fulfillment_engine.get_fulfillment_details(order_id, user_id)

        if fulfillment:
            return jsonify({
                'success': True,
                'fulfillment': fulfillment.to_dict()
            })
        else:
            return jsonify({'error': 'Fulfillment not found'}), 404

    except Exception as e:
        logger.error(f"Error getting fulfillment: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@fulfillment_bp.route('/orders/<int:order_id>', methods=['PUT'])
@jwt_required()
def update_fulfillment(order_id):
    """Update order fulfillment status"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        # Get fulfillment engine
        fulfillment_engine = OrderFulfillmentEngine.get_instance()

        # Update fulfillment
        result = fulfillment_engine.update_fulfillment_status(
            order_id=order_id,
            status=data.get('status'),
            notes=data.get('notes'),
            user_id=user_id
        )

        if result.success:
            return jsonify({
                'success': True,
                'status': result.status,
                'updated_at': result.updated_at.isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'error': result.error_message
            }), 400

    except Exception as e:
        logger.error(f"Error updating fulfillment: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@fulfillment_bp.route('/orders/<int:order_id>', methods=['DELETE'])
@jwt_required()
def cancel_fulfillment(order_id):
    """Cancel order fulfillment"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json() or {}

        # Get fulfillment engine
        fulfillment_engine = OrderFulfillmentEngine.get_instance()

        # Cancel fulfillment
        result = fulfillment_engine.cancel_fulfillment(
            order_id=order_id,
            reason=data.get('reason', 'User requested cancellation'),
            user_id=user_id
        )

        if result.success:
            return jsonify({
                'success': True,
                'status': 'cancelled',
                'refund_amount': result.refund_amount,
                'cancelled_at': result.cancelled_at.isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'error': result.error_message
            }), 400

    except Exception as e:
        logger.error(f"Error cancelling fulfillment: {e}")
        return jsonify({'error': 'Internal server error'}), 500

# ============================================================================
# RATE CALCULATION ENDPOINTS
# ============================================================================

@fulfillment_bp.route('/rates/calculate', methods=['POST'])
@jwt_required()
def calculate_shipping_rates():
    """Calculate shipping rates from multiple carriers"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['origin', 'destination', 'packages']
        is_valid, error_msg = validate_required_fields(data, required_fields)
        if not is_valid:
            return jsonify({'error': error_msg}), 400
        
        # Create address and package objects
        origin = create_address_from_dict(data['origin'])
        destination = create_address_from_dict(data['destination'])
        packages = [create_package_from_dict(pkg) for pkg in data['packages']]
        
        # Get carrier preferences
        carriers = []
        if 'carriers' in data:
            for carrier_name in data['carriers']:
                try:
                    carriers.append(ShippingCarrier(carrier_name))
                except ValueError:
                    logger.warning(f"Invalid carrier: {carrier_name}")
        
        # Calculate rates
        rates = rate_engine.calculate_rates(origin, destination, packages, carriers or None)
        
        # Convert to JSON-serializable format
        rates_data = [rate.to_dict() for rate in rates]
        
        return jsonify({
            'success': True,
            'rates': rates_data,
            'count': len(rates_data)
        })
        
    except CarrierAPIError as e:
        logger.error(f"Carrier API error: {str(e)}")
        return jsonify({'error': f'Carrier API error: {str(e)}'}), 500
    except Exception as e:
        logger.error(f"Rate calculation error: {str(e)}")
        return jsonify({'error': f'Rate calculation failed: {str(e)}'}), 500

@fulfillment_bp.route('/rates/best', methods=['POST'])
@jwt_required()
def get_best_rate():
    """Get the best shipping rate based on criteria"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['origin', 'destination', 'packages']
        is_valid, error_msg = validate_required_fields(data, required_fields)
        if not is_valid:
            return jsonify({'error': error_msg}), 400
        
        # Create address and package objects
        origin = create_address_from_dict(data['origin'])
        destination = create_address_from_dict(data['destination'])
        packages = [create_package_from_dict(pkg) for pkg in data['packages']]
        
        # Get criteria (cheapest, fastest, most_reliable)
        criteria = data.get('criteria', 'cheapest')
        
        # Get best rate
        best_rate = rate_engine.get_best_rate(origin, destination, packages, criteria)
        
        return jsonify({
            'success': True,
            'best_rate': best_rate.to_dict()
        })
        
    except CarrierAPIError as e:
        logger.error(f"Carrier API error: {str(e)}")
        return jsonify({'error': f'Carrier API error: {str(e)}'}), 500
    except Exception as e:
        logger.error(f"Best rate calculation error: {str(e)}")
        return jsonify({'error': f'Best rate calculation failed: {str(e)}'}), 500

# ============================================================================
# SHIPMENT CREATION ENDPOINTS
# ============================================================================

@fulfillment_bp.route('/shipments/create', methods=['POST'])
@jwt_required()
def create_shipment():
    """Create a new shipment with carrier"""
    try:
        # Get database models
        db, Shipment, CarrierModel, TrackingEventModel = get_models()
        if not db:
            return jsonify({'error': 'Database models not available'}), 500
        data = request.get_json()
        user_id = get_jwt_identity()
        
        # Validate required fields
        required_fields = ['order_id', 'origin', 'destination', 'packages']
        is_valid, error_msg = validate_required_fields(data, required_fields)
        if not is_valid:
            return jsonify({'error': error_msg}), 400
        
        # Create address and package objects
        origin = create_address_from_dict(data['origin'])
        destination = create_address_from_dict(data['destination'])
        packages = [create_package_from_dict(pkg) for pkg in data['packages']]
        
        # Get carrier and service type
        carrier = None
        if 'carrier' in data:
            try:
                carrier = ShippingCarrier(data['carrier'])
            except ValueError:
                return jsonify({'error': f"Invalid carrier: {data['carrier']}"}), 400
        
        service_type = data.get('service_type')
        reference_number = data.get('reference_number')
        
        # Create shipment
        result = shipment_manager.create_shipment(
            origin, destination, packages, carrier, service_type, reference_number
        )
        
        if result.get('success'):
            # Save shipment to database
            try:
                # Get or create carrier record
                carrier_record = CarrierModel.query.filter_by(code=carrier.value).first()
                if not carrier_record:
                    carrier_record = CarrierModel(
                        name=carrier.value.title(),
                        code=carrier.value,
                        is_active=True
                    )
                    db.session.add(carrier_record)
                    db.session.flush()
                
                # Create shipment record
                shipment = Shipment(
                    order_id=data['order_id'],
                    carrier_id=carrier_record.id,
                    tracking_number=result.get('tracking_number'),
                    service_type=service_type or 'Standard',
                    status='label_created',
                    weight_kg=sum(pkg.weight for pkg in packages),
                    dimensions_cm=json.dumps({
                        'length': max(pkg.length for pkg in packages),
                        'width': max(pkg.width for pkg in packages),
                        'height': max(pkg.height for pkg in packages)
                    }),
                    declared_value=sum(pkg.declared_value for pkg in packages),
                    origin_address=json.dumps(data['origin']),
                    destination_address=json.dumps(data['destination']),
                    shipping_cost=result.get('shipping_cost', 0),
                    estimated_delivery_date=result.get('estimated_delivery'),
                    label_url=result.get('label_url'),
                    carrier_reference=result.get('carrier_reference')
                )
                
                db.session.add(shipment)
                db.session.commit()
                
                logger.info(f"Created shipment {result.get('tracking_number')} for order {data['order_id']}")
                
            except Exception as db_error:
                logger.error(f"Database error: {str(db_error)}")
                db.session.rollback()
                # Continue with API response even if DB save fails
        
        return jsonify(result)
        
    except CarrierAPIError as e:
        logger.error(f"Carrier API error: {str(e)}")
        return jsonify({'error': f'Carrier API error: {str(e)}'}), 500
    except Exception as e:
        logger.error(f"Shipment creation error: {str(e)}")
        return jsonify({'error': f'Shipment creation failed: {str(e)}'}), 500

# ============================================================================
# TRACKING ENDPOINTS
# ============================================================================

@fulfillment_bp.route('/track/<tracking_number>', methods=['GET'])
def track_shipment(tracking_number):
    """Track shipment by tracking number"""
    try:
        # Get database models
        db, Shipment, CarrierModel, TrackingEventModel = get_models()
        if not db:
            return jsonify({'error': 'Database models not available'}), 500
        # Get carrier from query parameter or database
        carrier_param = request.args.get('carrier')
        
        if carrier_param:
            try:
                carrier = ShippingCarrier(carrier_param)
            except ValueError:
                return jsonify({'error': f"Invalid carrier: {carrier_param}"}), 400
        else:
            # Try to find carrier from database
            shipment = Shipment.query.filter_by(tracking_number=tracking_number).first()
            if not shipment:
                return jsonify({'error': 'Shipment not found'}), 404
            
            carrier_record = CarrierModel.query.get(shipment.carrier_id)
            if not carrier_record:
                return jsonify({'error': 'Carrier not found'}), 404
            
            try:
                carrier = ShippingCarrier(carrier_record.code)
            except ValueError:
                return jsonify({'error': f"Invalid carrier code: {carrier_record.code}"}), 400
        
        # Track shipment
        events = shipment_manager.track_shipment(tracking_number, carrier)
        
        # Convert events to JSON-serializable format
        events_data = []
        for event in events:
            events_data.append({
                'event_type': event.event_type.value,
                'status': event.status,
                'description': event.description,
                'location': event.location,
                'timestamp': event.timestamp.isoformat(),
                'carrier_code': event.carrier_code
            })
        
        return jsonify({
            'success': True,
            'tracking_number': tracking_number,
            'carrier': carrier.value,
            'events': events_data,
            'event_count': len(events_data)
        })
        
    except CarrierAPIError as e:
        logger.error(f"Tracking error: {str(e)}")
        return jsonify({'error': f'Tracking failed: {str(e)}'}), 500
    except Exception as e:
        logger.error(f"Tracking error: {str(e)}")
        return jsonify({'error': f'Tracking failed: {str(e)}'}), 500

@fulfillment_bp.route('/track/bulk', methods=['POST'])
@jwt_required()
def track_multiple_shipments():
    """Track multiple shipments"""
    try:
        data = request.get_json()
        
        if 'tracking_numbers' not in data:
            return jsonify({'error': 'tracking_numbers field is required'}), 400
        
        tracking_numbers = data['tracking_numbers']
        if not isinstance(tracking_numbers, list):
            return jsonify({'error': 'tracking_numbers must be a list'}), 400
        
        results = []
        
        for tracking_number in tracking_numbers:
            try:
                # Find shipment in database
                shipment = Shipment.query.filter_by(tracking_number=tracking_number).first()
                if not shipment:
                    results.append({
                        'tracking_number': tracking_number,
                        'error': 'Shipment not found'
                    })
                    continue
                
                carrier_record = CarrierModel.query.get(shipment.carrier_id)
                if not carrier_record:
                    results.append({
                        'tracking_number': tracking_number,
                        'error': 'Carrier not found'
                    })
                    continue
                
                carrier = ShippingCarrier(carrier_record.code)
                events = shipment_manager.track_shipment(tracking_number, carrier)
                
                # Convert events to JSON-serializable format
                events_data = []
                for event in events:
                    events_data.append({
                        'event_type': event.event_type.value,
                        'status': event.status,
                        'description': event.description,
                        'location': event.location,
                        'timestamp': event.timestamp.isoformat(),
                        'carrier_code': event.carrier_code
                    })
                
                results.append({
                    'tracking_number': tracking_number,
                    'carrier': carrier.value,
                    'events': events_data,
                    'success': True
                })
                
            except Exception as e:
                logger.error(f"Error tracking {tracking_number}: {str(e)}")
                results.append({
                    'tracking_number': tracking_number,
                    'error': str(e)
                })
        
        return jsonify({
            'success': True,
            'results': results,
            'total_count': len(tracking_numbers),
            'success_count': len([r for r in results if r.get('success')])
        })
        
    except Exception as e:
        logger.error(f"Bulk tracking error: {str(e)}")
        return jsonify({'error': f'Bulk tracking failed: {str(e)}'}), 500

# ============================================================================
# PICKUP SCHEDULING ENDPOINTS
# ============================================================================

@fulfillment_bp.route('/pickup/schedule', methods=['POST'])
@jwt_required()
def schedule_pickup():
    """Schedule pickup with carrier"""
    try:
        data = request.get_json()
        user_id = get_jwt_identity()

        # Validate required fields
        required_fields = ['pickup_address', 'packages', 'pickup_date', 'carrier']
        is_valid, error_msg = validate_required_fields(data, required_fields)
        if not is_valid:
            return jsonify({'error': error_msg}), 400

        # Create address and package objects
        pickup_address = create_address_from_dict(data['pickup_address'])
        packages = [create_package_from_dict(pkg) for pkg in data['packages']]

        # Parse pickup date
        try:
            pickup_date = datetime.strptime(data['pickup_date'], '%Y-%m-%d')
        except ValueError:
            return jsonify({'error': 'Invalid pickup_date format. Use YYYY-MM-DD'}), 400

        # Get carrier
        try:
            carrier = ShippingCarrier(data['carrier'])
        except ValueError:
            return jsonify({'error': f"Invalid carrier: {data['carrier']}"}), 400

        time_window = data.get('time_window', '10:00-18:00')

        # Schedule pickup
        result = shipment_manager.schedule_pickup(pickup_address, packages, pickup_date, carrier, time_window)

        return jsonify(result)

    except CarrierAPIError as e:
        logger.error(f"Pickup scheduling error: {str(e)}")
        return jsonify({'error': f'Pickup scheduling failed: {str(e)}'}), 500
    except Exception as e:
        logger.error(f"Pickup scheduling error: {str(e)}")
        return jsonify({'error': f'Pickup scheduling failed: {str(e)}'}), 500

# ============================================================================
# SHIPMENT MANAGEMENT ENDPOINTS
# ============================================================================

@fulfillment_bp.route('/shipments/<tracking_number>/cancel', methods=['POST'])
@jwt_required()
def cancel_shipment(tracking_number):
    """Cancel a shipment"""
    try:
        user_id = get_jwt_identity()

        # Find shipment in database
        shipment = Shipment.query.filter_by(tracking_number=tracking_number).first()
        if not shipment:
            return jsonify({'error': 'Shipment not found'}), 404

        # Get carrier
        carrier_record = CarrierModel.query.get(shipment.carrier_id)
        if not carrier_record:
            return jsonify({'error': 'Carrier not found'}), 404

        try:
            carrier = ShippingCarrier(carrier_record.code)
        except ValueError:
            return jsonify({'error': f"Invalid carrier code: {carrier_record.code}"}), 400

        # Cancel shipment
        success = shipment_manager.cancel_shipment(tracking_number, carrier)

        if success:
            # Update shipment status in database
            shipment.status = 'cancelled'
            db.session.commit()

            logger.info(f"Cancelled shipment {tracking_number}")

        return jsonify({
            'success': success,
            'tracking_number': tracking_number,
            'message': 'Shipment cancelled successfully' if success else 'Cancellation failed'
        })

    except Exception as e:
        logger.error(f"Cancellation error: {str(e)}")
        return jsonify({'error': f'Cancellation failed: {str(e)}'}), 500

@fulfillment_bp.route('/shipments', methods=['GET'])
@jwt_required()
def list_shipments():
    """List shipments with filtering options"""
    try:
        user_id = get_jwt_identity()

        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        status = request.args.get('status')
        carrier = request.args.get('carrier')
        order_id = request.args.get('order_id', type=int)

        # Build query
        query = Shipment.query

        if status:
            query = query.filter(Shipment.status == status)

        if carrier:
            carrier_record = CarrierModel.query.filter_by(code=carrier).first()
            if carrier_record:
                query = query.filter(Shipment.carrier_id == carrier_record.id)

        if order_id:
            query = query.filter(Shipment.order_id == order_id)

        # Order by creation date (newest first)
        query = query.order_by(Shipment.created_at.desc())

        # Paginate
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        shipments = pagination.items

        # Convert to JSON-serializable format
        shipments_data = []
        for shipment in shipments:
            carrier_record = CarrierModel.query.get(shipment.carrier_id)

            shipments_data.append({
                'id': shipment.id,
                'order_id': shipment.order_id,
                'tracking_number': shipment.tracking_number,
                'carrier': {
                    'name': carrier_record.name if carrier_record else 'Unknown',
                    'code': carrier_record.code if carrier_record else 'unknown'
                },
                'service_type': shipment.service_type,
                'status': shipment.status,
                'weight_kg': float(shipment.weight_kg) if shipment.weight_kg else 0,
                'declared_value': float(shipment.declared_value) if shipment.declared_value else 0,
                'shipping_cost': float(shipment.shipping_cost) if shipment.shipping_cost else 0,
                'estimated_delivery_date': shipment.estimated_delivery_date.isoformat() if shipment.estimated_delivery_date else None,
                'actual_delivery_date': shipment.actual_delivery_date.isoformat() if shipment.actual_delivery_date else None,
                'label_url': shipment.label_url,
                'created_at': shipment.created_at.isoformat(),
                'updated_at': shipment.updated_at.isoformat()
            })

        return jsonify({
            'success': True,
            'shipments': shipments_data,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        })

    except Exception as e:
        logger.error(f"List shipments error: {str(e)}")
        return jsonify({'error': f'Failed to list shipments: {str(e)}'}), 500

# ============================================================================
# WEBHOOK ENDPOINTS
# ============================================================================

@fulfillment_bp.route('/webhooks/<carrier_code>/tracking', methods=['POST'])
def handle_tracking_webhook(carrier_code):
    """Handle tracking updates from carriers"""
    try:
        data = request.get_json()

        logger.info(f"Received webhook from {carrier_code}: {data}")

        # Validate carrier
        try:
            carrier = ShippingCarrier(carrier_code)
        except ValueError:
            logger.error(f"Invalid carrier code in webhook: {carrier_code}")
            return jsonify({'error': 'Invalid carrier'}), 400

        # Extract tracking information based on carrier format
        tracking_number = None
        status = None
        location = None
        timestamp = None
        description = None

        if carrier_code == 'blue_dart':
            tracking_number = data.get('AWBNumber')
            status = data.get('Status')
            location = data.get('Location')
            timestamp_str = data.get('StatusDateTime')
            description = data.get('Instructions')
        elif carrier_code == 'delhivery':
            tracking_number = data.get('waybill')
            status = data.get('Status')
            location = data.get('Location')
            timestamp_str = data.get('StatusDateTime')
            description = data.get('Instructions')
        elif carrier_code == 'fedex':
            tracking_number = data.get('trackingNumber')
            status = data.get('eventType')
            location = data.get('scanLocation', {}).get('city', '')
            timestamp_str = data.get('date')
            description = data.get('eventDescription')

        if not tracking_number:
            logger.error(f"No tracking number in webhook from {carrier_code}")
            return jsonify({'error': 'No tracking number provided'}), 400

        # Find shipment in database
        shipment = Shipment.query.filter_by(tracking_number=tracking_number).first()
        if not shipment:
            logger.warning(f"Shipment not found for tracking number: {tracking_number}")
            return jsonify({'message': 'Shipment not found, but webhook processed'}), 200

        # Parse timestamp
        if timestamp_str:
            try:
                if carrier_code in ['blue_dart', 'delhivery']:
                    timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                elif carrier_code == 'fedex':
                    timestamp = datetime.strptime(timestamp_str, '%Y-%m-%dT%H:%M:%S')
            except ValueError:
                timestamp = datetime.now()
        else:
            timestamp = datetime.now()

        # Update shipment status
        if status:
            # Map carrier status to our standard status
            status_mapping = {
                'blue_dart': {
                    'Booked': 'label_created',
                    'Picked Up': 'picked_up',
                    'In Transit': 'in_transit',
                    'Out for Delivery': 'out_for_delivery',
                    'Delivered': 'delivered',
                    'Exception': 'exception',
                    'Returned': 'returned'
                },
                'delhivery': {
                    'Booked': 'label_created',
                    'Pickup': 'picked_up',
                    'In-Transit': 'in_transit',
                    'Out-for-Delivery': 'out_for_delivery',
                    'Delivered': 'delivered',
                    'Exception': 'exception',
                    'RTO': 'returned'
                },
                'fedex': {
                    'SH': 'label_created',
                    'PU': 'picked_up',
                    'IT': 'in_transit',
                    'OD': 'out_for_delivery',
                    'DL': 'delivered',
                    'EX': 'exception',
                    'RT': 'returned'
                }
            }

            mapped_status = status_mapping.get(carrier_code, {}).get(status, shipment.status)
            shipment.status = mapped_status

            # Update delivery date if delivered
            if mapped_status == 'delivered' and not shipment.actual_delivery_date:
                shipment.actual_delivery_date = timestamp

        # Create tracking event record
        tracking_event = TrackingEventModel(
            shipment_id=shipment.id,
            event_type=status or 'update',
            status=status or 'unknown',
            description=description or 'Status update',
            location=location or '',
            event_timestamp=timestamp,
            carrier_data=json.dumps(data)
        )

        db.session.add(tracking_event)
        db.session.commit()

        logger.info(f"Updated shipment {tracking_number} with status {status}")

        return jsonify({
            'success': True,
            'message': 'Webhook processed successfully',
            'tracking_number': tracking_number,
            'status': status
        })

    except Exception as e:
        logger.error(f"Webhook processing error: {str(e)}")
        return jsonify({'error': f'Webhook processing failed: {str(e)}'}), 500

# ============================================================================
# CARRIER INFORMATION ENDPOINTS
# ============================================================================

@fulfillment_bp.route('/carriers', methods=['GET'])
def list_carriers():
    """List available carriers and their capabilities"""
    try:
        # Get database models
        db, Shipment, CarrierModel, TrackingEventModel = get_models()
        if not CarrierModel:
            return jsonify({'error': 'Database models not available'}), 500

        carriers_info = []

        # Get carriers from database (ensure we're in app context)
        from flask import current_app
        with current_app.app_context():
            carriers = CarrierModel.query.filter_by(is_active=True).all()

        for carrier in carriers:
            carriers_info.append({
                'id': carrier.id,
                'name': carrier.name,
                'code': carrier.code,
                'supported_services': carrier.supported_services,
                'max_weight_kg': float(carrier.max_weight_kg) if carrier.max_weight_kg else None,
                'max_dimensions_cm': carrier.max_dimensions_cm,
                'is_active': carrier.is_active
            })

        return jsonify({
            'success': True,
            'carriers': carriers_info,
            'count': len(carriers_info)
        })

    except Exception as e:
        logger.error(f"List carriers error: {str(e)}")
        return jsonify({'error': f'Failed to list carriers: {str(e)}'}), 500

# ============================================================================
# ANALYTICS AND DASHBOARD ENDPOINTS
# ============================================================================

@fulfillment_bp.route('/analytics', methods=['GET'])
@jwt_required()
def get_fulfillment_analytics():
    """Get comprehensive fulfillment analytics"""
    try:
        user_id = get_jwt_identity()

        # Get query parameters
        days = request.args.get('days', 30, type=int)
        start_date = datetime.now() - timedelta(days=days)
        end_date = datetime.now()

        # Get fulfillment engine
        fulfillment_engine = OrderFulfillmentEngine.get_instance()

        # Get analytics data
        analytics = fulfillment_engine.get_fulfillment_analytics(start_date, end_date)

        return jsonify({
            'success': True,
            'analytics': analytics,
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'days': days
            }
        })

    except Exception as e:
        logger.error(f"Error getting analytics: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@fulfillment_bp.route('/analytics/carriers', methods=['GET'])
@jwt_required()
def get_carrier_performance():
    """Get carrier performance analytics"""
    try:
        user_id = get_jwt_identity()

        # Get query parameters
        days = request.args.get('days', 30, type=int)
        start_date = datetime.now() - timedelta(days=days)
        end_date = datetime.now()

        # Get carrier performance data
        from tracking_dashboard import TrackingDashboardService
        dashboard_service = TrackingDashboardService(db.session)

        performance_data = dashboard_service._get_carrier_performance(start_date, end_date)

        return jsonify({
            'success': True,
            'carrier_performance': performance_data,
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'days': days
            }
        })

    except Exception as e:
        logger.error(f"Error getting carrier performance: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@fulfillment_bp.route('/analytics/metrics', methods=['GET'])
@jwt_required()
def get_fulfillment_metrics():
    """Get key fulfillment metrics"""
    try:
        user_id = get_jwt_identity()

        # Get fulfillment engine
        fulfillment_engine = OrderFulfillmentEngine.get_instance()

        # Get metrics
        metrics = fulfillment_engine.get_fulfillment_metrics()

        return jsonify({
            'success': True,
            'metrics': metrics
        })

    except Exception as e:
        logger.error(f"Error getting metrics: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@fulfillment_bp.route('/dashboard/summary', methods=['GET'])
@jwt_required()
def get_dashboard_summary():
    """Get fulfillment dashboard summary"""
    try:
        user_id = get_jwt_identity()

        # Get query parameters
        days = request.args.get('days', 7, type=int)

        # Get tracking dashboard service
        from tracking_dashboard import TrackingDashboardService
        dashboard_service = TrackingDashboardService(db.session)

        # Get dashboard metrics
        metrics = dashboard_service.get_dashboard_metrics(days)

        # Get fulfillment engine metrics
        fulfillment_engine = OrderFulfillmentEngine.get_instance()
        fulfillment_metrics = fulfillment_engine.get_fulfillment_metrics()

        return jsonify({
            'success': True,
            'summary': {
                'tracking_metrics': metrics.to_dict(),
                'fulfillment_metrics': fulfillment_metrics,
                'period_days': days,
                'last_updated': datetime.now().isoformat()
            }
        })

    except Exception as e:
        logger.error(f"Error getting dashboard summary: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@fulfillment_bp.route('/dashboard/alerts', methods=['GET'])
@jwt_required()
def get_fulfillment_alerts():
    """Get fulfillment system alerts and exceptions"""
    try:
        user_id = get_jwt_identity()

        # Get tracking dashboard service
        from tracking_dashboard import TrackingDashboardService
        dashboard_service = TrackingDashboardService(db.session)

        # Get recent exceptions
        exceptions = dashboard_service._get_recent_exceptions(limit=20)

        # Get delayed shipments
        delayed_shipments = dashboard_service._get_delayed_shipments()

        # Get system alerts
        alerts = []

        # Check for high exception rate
        if len(exceptions) > 10:
            alerts.append({
                'type': 'warning',
                'message': f'High exception rate: {len(exceptions)} exceptions in recent activity',
                'severity': 'medium',
                'timestamp': datetime.now().isoformat()
            })

        # Check for delayed shipments
        if len(delayed_shipments) > 5:
            alerts.append({
                'type': 'warning',
                'message': f'{len(delayed_shipments)} shipments are delayed',
                'severity': 'high',
                'timestamp': datetime.now().isoformat()
            })

        return jsonify({
            'success': True,
            'alerts': alerts,
            'exceptions': exceptions,
            'delayed_shipments': delayed_shipments,
            'alert_count': len(alerts)
        })

    except Exception as e:
        logger.error(f"Error getting alerts: {e}")
        return jsonify({'error': 'Internal server error'}), 500

# ============================================================================
# BULK OPERATIONS ENDPOINTS
# ============================================================================

@fulfillment_bp.route('/bulk/process', methods=['POST'])
@jwt_required()
def bulk_process_orders():
    """Process multiple orders for fulfillment"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        # Validate required fields
        if 'order_ids' not in data or not isinstance(data['order_ids'], list):
            return jsonify({'error': 'order_ids must be a list'}), 400

        # Get fulfillment engine
        fulfillment_engine = OrderFulfillmentEngine.get_instance()

        results = []
        for order_id in data['order_ids']:
            try:
                result = fulfillment_engine.process_order_by_id(order_id, user_id)
                results.append({
                    'order_id': order_id,
                    'success': result.success,
                    'status': result.status if result.success else None,
                    'error': result.error_message if not result.success else None
                })
            except Exception as e:
                results.append({
                    'order_id': order_id,
                    'success': False,
                    'error': str(e)
                })

        # Calculate summary
        successful = sum(1 for r in results if r['success'])
        failed = len(results) - successful

        return jsonify({
            'success': True,
            'results': results,
            'summary': {
                'total': len(results),
                'successful': successful,
                'failed': failed,
                'success_rate': (successful / len(results)) * 100 if results else 0
            }
        })

    except Exception as e:
        logger.error(f"Error in bulk processing: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@fulfillment_bp.route('/bulk/track', methods=['POST'])
@jwt_required()
def bulk_track_shipments():
    """Track multiple shipments"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        # Validate required fields
        if 'tracking_numbers' not in data or not isinstance(data['tracking_numbers'], list):
            return jsonify({'error': 'tracking_numbers must be a list'}), 400

        # Get tracking system
        from tracking_system import get_tracking_system
        from app import db
        tracking_system = get_tracking_system(db.session)

        results = []
        for tracking_number in data['tracking_numbers']:
            try:
                tracking_info = tracking_system.get_tracking_info(tracking_number)
                if tracking_info:
                    results.append({
                        'tracking_number': tracking_number,
                        'success': True,
                        'status': tracking_info.current_status,
                        'last_updated': tracking_info.last_updated.isoformat(),
                        'carrier': tracking_info.carrier_code,
                        'events': [event.to_dict() for event in tracking_info.events[-5:]]  # Last 5 events
                    })
                else:
                    results.append({
                        'tracking_number': tracking_number,
                        'success': False,
                        'error': 'Tracking information not found'
                    })
            except Exception as e:
                results.append({
                    'tracking_number': tracking_number,
                    'success': False,
                    'error': str(e)
                })

        # Calculate summary
        successful = sum(1 for r in results if r['success'])
        failed = len(results) - successful

        return jsonify({
            'success': True,
            'results': results,
            'summary': {
                'total': len(results),
                'successful': successful,
                'failed': failed,
                'success_rate': (successful / len(results)) * 100 if results else 0
            }
        })

    except Exception as e:
        logger.error(f"Error in bulk tracking: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@fulfillment_bp.route('/bulk/cancel', methods=['POST'])
@jwt_required()
def bulk_cancel_shipments():
    """Cancel multiple shipments"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        # Validate required fields
        if 'tracking_numbers' not in data or not isinstance(data['tracking_numbers'], list):
            return jsonify({'error': 'tracking_numbers must be a list'}), 400

        reason = data.get('reason', 'Bulk cancellation requested')

        results = []
        for tracking_number in data['tracking_numbers']:
            try:
                # Find shipment in database
                shipment = Shipment.query.filter_by(tracking_number=tracking_number).first()

                if shipment:
                    # Check if shipment can be cancelled
                    if shipment.status in ['delivered', 'cancelled']:
                        results.append({
                            'tracking_number': tracking_number,
                            'success': False,
                            'error': f'Cannot cancel shipment with status: {shipment.status}'
                        })
                        continue

                    # Cancel with carrier
                    try:
                        carrier_factory = CarrierFactory()
                        carrier_api = carrier_factory.get_carrier(ShippingCarrier(shipment.carrier.code))

                        cancel_result = carrier_api.cancel_shipment(tracking_number)

                        if cancel_result.get('success'):
                            # Update shipment status
                            shipment.status = 'cancelled'
                            shipment.cancelled_at = datetime.now()
                            shipment.cancellation_reason = reason

                            db.session.commit()

                            results.append({
                                'tracking_number': tracking_number,
                                'success': True,
                                'status': 'cancelled',
                                'cancelled_at': shipment.cancelled_at.isoformat()
                            })
                        else:
                            results.append({
                                'tracking_number': tracking_number,
                                'success': False,
                                'error': cancel_result.get('error', 'Carrier cancellation failed')
                            })

                    except Exception as carrier_error:
                        results.append({
                            'tracking_number': tracking_number,
                            'success': False,
                            'error': f'Carrier API error: {str(carrier_error)}'
                        })
                else:
                    results.append({
                        'tracking_number': tracking_number,
                        'success': False,
                        'error': 'Shipment not found'
                    })

            except Exception as e:
                results.append({
                    'tracking_number': tracking_number,
                    'success': False,
                    'error': str(e)
                })

        # Calculate summary
        successful = sum(1 for r in results if r['success'])
        failed = len(results) - successful

        return jsonify({
            'success': True,
            'results': results,
            'summary': {
                'total': len(results),
                'successful': successful,
                'failed': failed,
                'success_rate': (successful / len(results)) * 100 if results else 0
            }
        })

    except Exception as e:
        logger.error(f"Error in bulk cancellation: {e}")
        return jsonify({'error': 'Internal server error'}), 500

# ============================================================================
# CUSTOMER TRACKING ENDPOINTS
# ============================================================================

@fulfillment_bp.route('/track/<tracking_number>', methods=['GET'])
def track_by_number(tracking_number):
    """Track shipment by tracking number - Public endpoint for customers"""
    try:
        # Get tracking system
        from tracking_system import get_tracking_system
        from app import db
        tracking_system = get_tracking_system(db.session)

        # Get tracking information
        tracking_info = tracking_system.get_tracking_info(tracking_number)

        if not tracking_info:
            return jsonify({'error': 'Tracking number not found'}), 404

        # Format response for customer interface
        response_data = {
            'tracking_number': tracking_info.tracking_number,
            'current_status': tracking_info.current_status.value if tracking_info.current_status else None,
            'estimated_delivery': tracking_info.estimated_delivery.isoformat() if tracking_info.estimated_delivery else None,
            'actual_delivery': tracking_info.actual_delivery.isoformat() if tracking_info.actual_delivery else None,
            'origin_location': tracking_info.origin_location,
            'destination_location': tracking_info.destination_location,
            'carrier_code': tracking_info.carrier_code,
            'carrier_name': get_carrier_name(tracking_info.carrier_code),
            'last_updated': tracking_info.last_updated.isoformat() if tracking_info.last_updated else None,
            'is_delayed': tracking_info.is_delayed,
            'delay_reason': tracking_info.delay_reason,
            'events': []
        }

        # Add tracking events
        for event in tracking_info.events:
            response_data['events'].append({
                'status': event.status.value if event.status else None,
                'description': event.description,
                'location': event.location,
                'timestamp': event.timestamp.isoformat() if event.timestamp else None,
                'carrier_code': event.carrier_code,
                'facility_name': getattr(event, 'facility_name', None),
                'exception_code': getattr(event, 'exception_code', None),
                'exception_description': getattr(event, 'exception_description', None)
            })

        return jsonify(response_data)

    except Exception as e:
        logger.error(f"Error tracking shipment {tracking_number}: {e}")
        return jsonify({'error': 'Failed to retrieve tracking information'}), 500

@fulfillment_bp.route('/orders/<int:order_id>/tracking', methods=['GET'])
def track_by_order_id(order_id):
    """Track shipment by order ID - Requires authentication"""
    try:
        # Get order from database
        from app import Order
        order = db.session.query(Order).filter(Order.id == order_id).first()

        if not order:
            return jsonify({'error': 'Order not found'}), 404

        # Check if order has tracking number
        if not order.tracking_number:
            return jsonify({'error': 'No tracking information available for this order'}), 404

        # Get tracking information using tracking number
        from tracking_system import get_tracking_system
        from app import db
        tracking_system = get_tracking_system(db.session)
        tracking_info = tracking_system.get_tracking_info(order.tracking_number)

        if not tracking_info:
            return jsonify({'error': 'Tracking information not available'}), 404

        # Format response with order details
        response_data = {
            'order_id': order.id,
            'order_number': order.order_number,
            'tracking_number': tracking_info.tracking_number,
            'current_status': tracking_info.current_status.value if tracking_info.current_status else None,
            'estimated_delivery': tracking_info.estimated_delivery.isoformat() if tracking_info.estimated_delivery else None,
            'actual_delivery': tracking_info.actual_delivery.isoformat() if tracking_info.actual_delivery else None,
            'origin_location': tracking_info.origin_location,
            'destination_location': tracking_info.destination_location,
            'carrier_code': tracking_info.carrier_code,
            'carrier_name': get_carrier_name(tracking_info.carrier_code),
            'last_updated': tracking_info.last_updated.isoformat() if tracking_info.last_updated else None,
            'is_delayed': tracking_info.is_delayed,
            'delay_reason': tracking_info.delay_reason,
            'shipping_address': order.shipping_address,
            'events': []
        }

        # Add tracking events
        for event in tracking_info.events:
            response_data['events'].append({
                'status': event.status.value if event.status else None,
                'description': event.description,
                'location': event.location,
                'timestamp': event.timestamp.isoformat() if event.timestamp else None,
                'carrier_code': event.carrier_code,
                'facility_name': getattr(event, 'facility_name', None),
                'exception_code': getattr(event, 'exception_code', None),
                'exception_description': getattr(event, 'exception_description', None)
            })

        return jsonify(response_data)

    except Exception as e:
        logger.error(f"Error tracking order {order_id}: {e}")
        return jsonify({'error': 'Failed to retrieve tracking information'}), 500

def get_carrier_name(carrier_code):
    """Helper function to get carrier name from code"""
    carrier_names = {
        'BLUEDART': 'Blue Dart Express',
        'DELHIVERY': 'Delhivery',
        'FEDEX': 'FedEx',
        'DHL': 'DHL Express',
        'DTDC': 'DTDC Express',
        'ECOM': 'Ecom Express'
    }
    return carrier_names.get(carrier_code, carrier_code)
