"""
Webhook Handlers for Carrier Updates
====================================

Handles incoming webhook notifications from shipping carriers
for real-time tracking updates and status synchronization.

Features:
1. Multi-carrier webhook processing
2. Signature verification for security
3. Event deduplication
4. Automatic retry handling
5. Error logging and monitoring
6. Integration with tracking system
"""

import logging
import json
import hmac
import hashlib
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass
import threading

from flask import Blueprint, request, jsonify
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

# ============================================================================
# WEBHOOK CONFIGURATION
# ============================================================================

class WebhookCarrier(Enum):
    """Supported carriers for webhooks"""
    BLUE_DART = "blue_dart"
    DELHIVERY = "delhivery"
    FEDEX = "fedex"

@dataclass
class WebhookConfig:
    """Webhook configuration for each carrier"""
    carrier: WebhookCarrier
    secret_key: str
    signature_header: str
    signature_method: str  # 'hmac_sha256', 'hmac_sha1', etc.
    event_field: str  # Field containing event type
    tracking_field: str  # Field containing tracking number
    status_field: str  # Field containing status
    timestamp_field: str  # Field containing timestamp

import os

# Webhook configurations for each carrier
WEBHOOK_CONFIGS = {
    WebhookCarrier.BLUE_DART: WebhookConfig(
        carrier=WebhookCarrier.BLUE_DART,
        secret_key=os.getenv("BLUE_DART_WEBHOOK_SECRET", "your_blue_dart_webhook_secret"),
        signature_header="X-BlueDart-Signature",
        signature_method="hmac_sha256",
        event_field="event_type",
        tracking_field="tracking_number",
        status_field="status",
        timestamp_field="timestamp"
    ),
    WebhookCarrier.DELHIVERY: WebhookConfig(
        carrier=WebhookCarrier.DELHIVERY,
        secret_key=os.getenv("DELHIVERY_WEBHOOK_SECRET", "your_delhivery_webhook_secret"),
        signature_header="X-Delhivery-Signature",
        signature_method="hmac_sha256",
        event_field="event",
        tracking_field="waybill",
        status_field="status",
        timestamp_field="updated_at"
    ),
    WebhookCarrier.FEDEX: WebhookConfig(
        carrier=WebhookCarrier.FEDEX,
        secret_key=os.getenv("FEDEX_WEBHOOK_SECRET", "your_fedex_webhook_secret"),
        signature_header="X-FedEx-Signature",
        signature_method="hmac_sha256",
        event_field="eventType",
        tracking_field="trackingNumber",
        status_field="statusDescription",
        timestamp_field="eventDateTime"
    )
}

# ============================================================================
# WEBHOOK SECURITY
# ============================================================================

class WebhookSecurity:
    """Handles webhook signature verification and security"""
    
    @staticmethod
    def verify_signature(payload: bytes, signature: str, secret: str, method: str) -> bool:
        """Verify webhook signature"""
        try:
            if method == "hmac_sha256":
                expected_signature = hmac.new(
                    secret.encode('utf-8'),
                    payload,
                    hashlib.sha256
                ).hexdigest()
                
                # Handle different signature formats
                if signature.startswith('sha256='):
                    signature = signature[7:]
                
                return hmac.compare_digest(expected_signature, signature)
                
            elif method == "hmac_sha1":
                expected_signature = hmac.new(
                    secret.encode('utf-8'),
                    payload,
                    hashlib.sha1
                ).hexdigest()
                
                if signature.startswith('sha1='):
                    signature = signature[6:]
                
                return hmac.compare_digest(expected_signature, signature)
            
            else:
                logger.error(f"Unsupported signature method: {method}")
                return False
                
        except Exception as e:
            logger.error(f"Error verifying webhook signature: {e}")
            return False
    
    @staticmethod
    def is_valid_ip(ip_address: str, carrier: WebhookCarrier) -> bool:
        """Check if IP address is from valid carrier range"""
        # Define allowed IP ranges for each carrier
        ALLOWED_IPS = {
            WebhookCarrier.BLUE_DART: [
                "************/22",
                "************/22",
                # Add Blue Dart IP ranges
            ],
            WebhookCarrier.DELHIVERY: [
                "*********/16",
                "**********/15",
                # Add Delhivery IP ranges
            ],
            WebhookCarrier.FEDEX: [
                "***********/16",
                "***********/16",
                # Add FedEx IP ranges
            ]
        }
        
        # For now, return True (implement proper IP validation in production)
        return True

# ============================================================================
# WEBHOOK EVENT PROCESSING
# ============================================================================

class WebhookEventProcessor:
    """Processes webhook events from carriers"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.processed_events = set()  # Simple deduplication
        self.processing_lock = threading.RLock()
    
    def process_webhook_event(self, carrier: WebhookCarrier, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Process incoming webhook event"""
        try:
            with self.processing_lock:
                # Extract event information
                config = WEBHOOK_CONFIGS[carrier]
                
                tracking_number = self._extract_field(payload, config.tracking_field)
                status = self._extract_field(payload, config.status_field)
                timestamp_str = self._extract_field(payload, config.timestamp_field)
                event_type = self._extract_field(payload, config.event_field)
                
                if not tracking_number or not status:
                    return {'error': 'Missing required fields', 'success': False}
                
                # Create event ID for deduplication
                event_id = f"{carrier.value}_{tracking_number}_{timestamp_str}_{status}"
                
                if event_id in self.processed_events:
                    logger.info(f"Duplicate event ignored: {event_id}")
                    return {'message': 'Duplicate event ignored', 'success': True}
                
                # Parse timestamp
                timestamp = self._parse_webhook_timestamp(timestamp_str, carrier)
                
                # Create tracking event
                tracking_event = self._create_tracking_event(
                    carrier, tracking_number, status, timestamp, event_type, payload
                )
                
                # Update tracking system
                success = self._update_tracking_system(tracking_event)
                
                if success:
                    self.processed_events.add(event_id)
                    
                    # Clean up old processed events (keep last 10000)
                    if len(self.processed_events) > 10000:
                        # Remove oldest 1000 events
                        old_events = list(self.processed_events)[:1000]
                        for old_event in old_events:
                            self.processed_events.discard(old_event)
                    
                    logger.info(f"Processed webhook event for {tracking_number}: {status}")
                    
                    return {
                        'message': 'Event processed successfully',
                        'tracking_number': tracking_number,
                        'status': status,
                        'success': True
                    }
                else:
                    return {'error': 'Failed to update tracking system', 'success': False}
                
        except Exception as e:
            logger.error(f"Error processing webhook event: {e}")
            return {'error': f'Processing failed: {str(e)}', 'success': False}
    
    def _extract_field(self, payload: Dict[str, Any], field_path: str) -> Optional[str]:
        """Extract field from nested payload using dot notation"""
        try:
            value = payload
            for key in field_path.split('.'):
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return None
            
            return str(value) if value is not None else None
            
        except Exception as e:
            logger.error(f"Error extracting field {field_path}: {e}")
            return None
    
    def _parse_webhook_timestamp(self, timestamp_str: str, carrier: WebhookCarrier) -> datetime:
        """Parse timestamp from webhook payload"""
        try:
            # Different carriers use different timestamp formats
            if carrier == WebhookCarrier.BLUE_DART:
                # Blue Dart format: "2024-01-15 14:30:00"
                return datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
            
            elif carrier == WebhookCarrier.DELHIVERY:
                # Delhivery format: "2024-01-15T14:30:00Z"
                if timestamp_str.endswith('Z'):
                    timestamp_str = timestamp_str[:-1]
                return datetime.strptime(timestamp_str, "%Y-%m-%dT%H:%M:%S")
            
            elif carrier == WebhookCarrier.FEDEX:
                # FedEx format: "2024-01-15T14:30:00.000Z"
                if timestamp_str.endswith('Z'):
                    timestamp_str = timestamp_str[:-1]
                if '.' in timestamp_str:
                    return datetime.strptime(timestamp_str, "%Y-%m-%dT%H:%M:%S.%f")
                else:
                    return datetime.strptime(timestamp_str, "%Y-%m-%dT%H:%M:%S")
            
            else:
                # Fallback to current time
                logger.warning(f"Unknown carrier timestamp format: {carrier}")
                return datetime.now()
                
        except Exception as e:
            logger.error(f"Error parsing timestamp {timestamp_str}: {e}")
            return datetime.now()
    
    def _create_tracking_event(self, carrier: WebhookCarrier, tracking_number: str, 
                             status: str, timestamp: datetime, event_type: str, 
                             payload: Dict[str, Any]) -> Dict[str, Any]:
        """Create standardized tracking event from webhook data"""
        from tracking_system import TrackingEvent, TrackingEventType, TrackingStatus, CarrierStatusMapper
        
        # Map carrier status to standardized status
        status_mapper = CarrierStatusMapper()
        standardized_status = status_mapper.map_status(carrier.value, status)
        
        # Determine event type
        if 'exception' in status.lower() or 'error' in status.lower():
            event_type_enum = TrackingEventType.EXCEPTION_OCCURRED
        elif 'attempt' in status.lower():
            event_type_enum = TrackingEventType.DELIVERY_ATTEMPT
        else:
            event_type_enum = TrackingEventType.STATUS_UPDATE
        
        # Extract additional information
        location = payload.get('location') or payload.get('current_location')
        description = payload.get('description') or payload.get('remarks')
        estimated_delivery = payload.get('estimated_delivery')
        
        return {
            'tracking_number': tracking_number,
            'status': standardized_status,
            'event_type': event_type_enum,
            'timestamp': timestamp,
            'location': location,
            'description': description,
            'carrier_code': carrier.value,
            'carrier_status': status,
            'estimated_delivery': estimated_delivery,
            'exception_code': payload.get('exception_code'),
            'exception_description': payload.get('exception_description'),
            'metadata': {
                'webhook_payload': payload,
                'original_event_type': event_type
            }
        }
    
    def _update_tracking_system(self, tracking_event: Dict[str, Any]) -> bool:
        """Update the tracking system with new event"""
        try:
            from tracking_system import get_tracking_system
            
            tracking_system = get_tracking_system(self.db)
            
            # Get existing tracking info
            tracking_info = tracking_system.get_tracking_info(tracking_event['tracking_number'])
            
            if not tracking_info:
                logger.warning(f"No tracking info found for {tracking_event['tracking_number']}")
                return False
            
            # Create tracking event object
            from tracking_system import TrackingEvent
            
            event = TrackingEvent(
                tracking_number=tracking_event['tracking_number'],
                status=tracking_event['status'],
                event_type=tracking_event['event_type'],
                timestamp=tracking_event['timestamp'],
                location=tracking_event['location'],
                description=tracking_event['description'],
                carrier_code=tracking_event['carrier_code'],
                carrier_status=tracking_event['carrier_status'],
                estimated_delivery=tracking_event['estimated_delivery'],
                exception_code=tracking_event['exception_code'],
                exception_description=tracking_event['exception_description'],
                metadata=tracking_event['metadata']
            )
            
            # Add event to tracking info
            tracking_info.events.append(event)
            tracking_info.current_status = event.status
            tracking_info.last_updated = datetime.now()
            
            # Update estimated delivery if provided
            if event.estimated_delivery:
                tracking_info.estimated_delivery = event.estimated_delivery
            
            # Mark as delivered if status is delivered
            if event.status.value == 'delivered':
                tracking_info.actual_delivery = event.timestamp
                tracking_system.stop_tracking(tracking_event['tracking_number'])
            
            # Save to database
            tracking_system._save_tracking_events(tracking_info, [event])
            
            # Trigger notifications
            tracking_system._trigger_notifications(tracking_info, [event])
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating tracking system: {e}")
            return False

# ============================================================================
# FLASK BLUEPRINT FOR WEBHOOK ENDPOINTS
# ============================================================================

webhook_bp = Blueprint('webhooks', __name__, url_prefix='/api/webhooks')

# Global event processor
_event_processor = None
_processor_lock = threading.Lock()

def get_event_processor(db_session: Session) -> WebhookEventProcessor:
    """Get global event processor instance"""
    global _event_processor
    
    if _event_processor is None:
        with _processor_lock:
            if _event_processor is None:
                _event_processor = WebhookEventProcessor(db_session)
    
    return _event_processor

@webhook_bp.route('/blue-dart', methods=['POST'])
def blue_dart_webhook():
    """Handle Blue Dart webhook notifications"""
    try:
        # Get request data
        payload = request.get_data()
        signature = request.headers.get('X-BlueDart-Signature', '')
        
        # Verify signature
        config = WEBHOOK_CONFIGS[WebhookCarrier.BLUE_DART]
        if not WebhookSecurity.verify_signature(payload, signature, config.secret_key, config.signature_method):
            logger.warning("Invalid Blue Dart webhook signature")
            return jsonify({'error': 'Invalid signature'}), 401
        
        # Parse JSON payload
        try:
            data = json.loads(payload.decode('utf-8'))
        except json.JSONDecodeError:
            return jsonify({'error': 'Invalid JSON payload'}), 400
        
        # Process event
        from app import db
        processor = get_event_processor(db.session)
        result = processor.process_webhook_event(WebhookCarrier.BLUE_DART, data)
        
        return jsonify(result), 200 if result.get('success') else 400
        
    except Exception as e:
        logger.error(f"Error processing Blue Dart webhook: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@webhook_bp.route('/delhivery', methods=['POST'])
def delhivery_webhook():
    """Handle Delhivery webhook notifications"""
    try:
        # Get request data
        payload = request.get_data()
        signature = request.headers.get('X-Delhivery-Signature', '')
        
        # Verify signature
        config = WEBHOOK_CONFIGS[WebhookCarrier.DELHIVERY]
        if not WebhookSecurity.verify_signature(payload, signature, config.secret_key, config.signature_method):
            logger.warning("Invalid Delhivery webhook signature")
            return jsonify({'error': 'Invalid signature'}), 401
        
        # Parse JSON payload
        try:
            data = json.loads(payload.decode('utf-8'))
        except json.JSONDecodeError:
            return jsonify({'error': 'Invalid JSON payload'}), 400
        
        # Process event
        from app import db
        processor = get_event_processor(db.session)
        result = processor.process_webhook_event(WebhookCarrier.DELHIVERY, data)
        
        return jsonify(result), 200 if result.get('success') else 400
        
    except Exception as e:
        logger.error(f"Error processing Delhivery webhook: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@webhook_bp.route('/fedex', methods=['POST'])
def fedex_webhook():
    """Handle FedEx webhook notifications"""
    try:
        # Get request data
        payload = request.get_data()
        signature = request.headers.get('X-FedEx-Signature', '')
        
        # Verify signature
        config = WEBHOOK_CONFIGS[WebhookCarrier.FEDEX]
        if not WebhookSecurity.verify_signature(payload, signature, config.secret_key, config.signature_method):
            logger.warning("Invalid FedEx webhook signature")
            return jsonify({'error': 'Invalid signature'}), 401
        
        # Parse JSON payload
        try:
            data = json.loads(payload.decode('utf-8'))
        except json.JSONDecodeError:
            return jsonify({'error': 'Invalid JSON payload'}), 400
        
        # Process event
        from app import db
        processor = get_event_processor(db.session)
        result = processor.process_webhook_event(WebhookCarrier.FEDEX, data)
        
        return jsonify(result), 200 if result.get('success') else 400
        
    except Exception as e:
        logger.error(f"Error processing FedEx webhook: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@webhook_bp.route('/test', methods=['POST'])
def test_webhook():
    """Test webhook endpoint for development"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400
        
        carrier_str = data.get('carrier', 'blue_dart')
        
        try:
            carrier = WebhookCarrier(carrier_str)
        except ValueError:
            return jsonify({'error': f'Invalid carrier: {carrier_str}'}), 400
        
        # Process test event
        from app import db
        processor = get_event_processor(db.session)
        result = processor.process_webhook_event(carrier, data)
        
        return jsonify(result), 200 if result.get('success') else 400
        
    except Exception as e:
        logger.error(f"Error processing test webhook: {e}")
        return jsonify({'error': 'Internal server error'}), 500
